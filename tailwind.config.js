/** @type {import('tailwindcss').Config} */
export default {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      colors: {
        textColorPrimary: "rgb(var(--text-color-primary)/ <alpha-value>)",
        textColorPrimaryDark:
          "rgb(var(--text-color-primary-dark)/ <alpha-value>)",
        textColorSvip: "rgb(var(--text-color-svip)/ <alpha-value>)",
        textColorOrange: "rgb(var(--text-color-orange)/ <alpha-value>)",
        textColorBlue: "rgb(var(--text-color-blue)/ <alpha-value>)",
        textColorGold: "rgb(var(--text-color-gold)/ <alpha-value>)",
        iconColorPrimary: "rgb(var(--icon-color-primary)/ <alpha-value>)",
        iconColorGold: "rgb(var(--icon-color-gold)/ <alpha-value>)",
        gradientColorPrimary1:
          "rgb(var(--gradient-color-primary1)/ <alpha-value>)",
        gradientColorPrimary2:
          "rgb(var(--gradient-color-primary2)/ <alpha-value>)",
        gradientColorPrimary3:
          "rgb(var(--gradient-color-primary3)/ <alpha-value>)",
        gradientGoldStart: "#F4E3C2",
        gradientGoldEnd: "#DEBD81",
        disabledGrey: "rgb(var(--disabled-grey)/ <alpha-value>)",
        disabledIconGrey: "rgb(var(--disabled-icon-grey)/ <alpha-value>)",
        unselectedButtonColor:
          "rgb(var(--unselected-button-color)/ <alpha-value>)",
      },
      boxShadow: {
        "3xl": "0 4px 4px -0px rgba(0, 0, 0, 0.3)",
      },
      flex: {
        2: "2 2 0%",
        3: "3 3 0%",
        4: "4 4 0%",
        5: "5 5 0%",
      },
    },
  },
  plugins: [
    function ({ addComponents }) {
      addComponents({
        '.gradient-primary': {
          '@apply bg-gradient-to-b from-gradientColorPrimary1 from-0% to-gradientColorPrimary2 to-100%': {}
        },
        '.gradient-gold': {
          '@apply bg-gradient-to-b from-gradientGoldStart from-0% to-gradientGoldEnd to-100%': {}
        },
        '.pt-safe': {
          'padding-top': 'env(safe-area-inset-top)'
        }
      })
    },
  ],
};
