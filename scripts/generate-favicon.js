// scripts/generate-favicon.js
import sharp from 'sharp';
import fsExtra from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';

// Helper to get __dirname in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Generate favicon files from a source logo image
 * @param {string} sourceLogoPath - Path to the source logo PNG file
 * @param {string} publicDir - Path to the public directory
 */
export async function generateFavicon(sourceLogoPath, publicDir) {
  try {
    console.log(`\nGenerating favicon from: ${sourceLogoPath}`);
    
    // Check if source logo exists
    const logoExists = await fsExtra.pathExists(sourceLogoPath);
    if (!logoExists) {
      throw new Error(`Source logo file does not exist: ${sourceLogoPath}`);
    }

    // Define favicon sizes and formats
    const faviconSizes = [
      { size: 16, name: 'favicon-16x16.png' },
      { size: 32, name: 'favicon-32x32.png' },
      { size: 48, name: 'favicon-48x48.png' },
      { size: 192, name: 'android-chrome-192x192.png' },
      { size: 512, name: 'android-chrome-512x512.png' }
    ];

    // Generate PNG favicons
    for (const favicon of faviconSizes) {
      const outputPath = path.join(publicDir, favicon.name);
      console.log(`Generating ${favicon.name} (${favicon.size}x${favicon.size})...`);
      
      await sharp(sourceLogoPath)
        .resize(favicon.size, favicon.size, {
          fit: 'contain',
          background: { r: 255, g: 255, b: 255, alpha: 0 } // Transparent background
        })
        .png()
        .toFile(outputPath);
    }

    // Generate ICO favicon (16x16, 32x32, 48x48 combined)
    const icoPath = path.join(publicDir, 'favicon.ico');
    console.log('Generating favicon.ico...');
    
    // For ICO, we'll use the 32x32 size as the main favicon
    await sharp(sourceLogoPath)
      .resize(32, 32, {
        fit: 'contain',
        background: { r: 255, g: 255, b: 255, alpha: 0 }
      })
      .png()
      .toFile(icoPath.replace('.ico', '-temp.png'));

    // Convert PNG to ICO (Sharp doesn't support ICO output, so we'll use PNG for now)
    // For a proper ICO file, you might want to use a different library like 'to-ico'
    await fsExtra.copy(icoPath.replace('.ico', '-temp.png'), icoPath);
    await fsExtra.remove(icoPath.replace('.ico', '-temp.png'));

    // Generate Apple Touch Icon
    const appleTouchIconPath = path.join(publicDir, 'apple-touch-icon.png');
    console.log('Generating apple-touch-icon.png (180x180)...');
    
    await sharp(sourceLogoPath)
      .resize(180, 180, {
        fit: 'contain',
        background: { r: 255, g: 255, b: 255, alpha: 0 }
      })
      .png()
      .toFile(appleTouchIconPath);

    // Generate web app manifest icons
    console.log('Favicon generation completed successfully!');
    
    return {
      favicon16: 'favicon-16x16.png',
      favicon32: 'favicon-32x32.png',
      favicon48: 'favicon-48x48.png',
      faviconIco: 'favicon.ico',
      appleTouchIcon: 'apple-touch-icon.png',
      androidChrome192: 'android-chrome-192x192.png',
      androidChrome512: 'android-chrome-512x512.png'
    };

  } catch (error) {
    console.error('Error generating favicon:', error);
    throw error;
  }
}

/**
 * Standalone script execution
 */
if (import.meta.url === `file://${process.argv[1]}`) {
  const logoNumber = process.argv[2] || '1';
  const sourceLogoPath = path.join(__dirname, '..', 'logo', `${logoNumber}.png`);
  const publicDir = path.join(__dirname, '..', 'public');

  generateFavicon(sourceLogoPath, publicDir)
    .then((result) => {
      console.log('Favicon files generated:', result);
    })
    .catch((error) => {
      console.error('Failed to generate favicon:', error);
      process.exit(1);
    });
}
