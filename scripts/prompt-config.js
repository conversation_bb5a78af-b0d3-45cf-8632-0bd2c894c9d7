// scripts/prompt-config.js
import prompts from 'prompts';
import { spawn } from 'child_process';
import fsExtra from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';

// Helper to get __dirname in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const commandArg = process.argv[2]; // 'dev' or 'build'

if (!commandArg || !['dev', 'build'].includes(commandArg)) {
  console.error('Usage: node scripts/prompt-config.js <dev|build>');
  process.exit(1);
}

const questions = [
  {
    type: 'text',
    name: 'appName',
    message: 'Enter the app name:',
    initial: '西瓜视频',
    validate: value => value ? true : 'App name cannot be empty'
  },
  {
    type: 'select',
    name: 'themeColor',
    message: 'Select theme color:',
    choices: [
      { title: 'Green', value: 'green' },
      { title: 'Blue', value: 'blue' },
      { title: 'Orange', value: 'orange' },
      { title: 'Black', value: 'black' },
      { title: 'Pink', value: 'pink' },
    ],
    initial: 0
  },
  {
    type: 'text',
    name: 'appKey',
    message: 'Enter the app key:',
    initial: 'l1ocvr2hrhp3jlcu',
    validate: value => value ? true : 'App key cannot be empty'
  },
  {
    type: 'toggle',
    name: 'debugMode',
    message: 'Enable debug mode? (Keeps console.log in build)',
    initial: false,
    active: 'yes',
    inactive: 'no'
  },
  {
    type: 'number',
    name: 'logoNumber',
    message: 'Select logo number (1-30):',
    initial: 1,
    min: 1,
    max: 30,
    validate: value => (value >= 1 && value <= 30) ? true : 'Logo number must be between 1 and 30'
  }
];

(async () => {
  try {
    let appName, themeColor, appKey, debugMode, logoNumber;

    // Check for environment variables first (for CI/CD)
    const envAppName = process.env.VITE_APP_NAME;
    const envThemeColor = process.env.VITE_THEME_COLOR;
    const envAppKey = process.env.VITE_APP_KEY;
    const envDebugMode = process.env.VITE_DEBUG; // Check for DEBUG env var
    const envLogoNumber = process.env.VITE_LOGO_NUMBER; // Check for logo number env var
    console.log('envAppName', envAppName);
    if (envAppName && envThemeColor && envAppKey && envDebugMode !== undefined && envLogoNumber) { // Check if all vars are set
      console.log('Using environment variables for configuration:');
      console.log(`  VITE_APP_NAME: ${envAppName}`);
      console.log(`  VITE_THEME_COLOR: ${envThemeColor}`);
      console.log(`  VITE_APP_KEY: ${envAppKey}`);
      console.log(`  VITE_DEBUG: ${envDebugMode}`); // Log debug mode
      console.log(`  VITE_LOGO_NUMBER: ${envLogoNumber}`); // Log logo number
      appName = envAppName;
      themeColor = envThemeColor;
      appKey = envAppKey;
      debugMode = envDebugMode === 'true'; // Convert string 'true' to boolean
      logoNumber = parseInt(envLogoNumber, 10); // Convert string to number
    } else {
      // Fallback to interactive prompts if env vars are not fully set
      console.log('Environment variables not fully set, proceeding with interactive prompts...');
      const response = await prompts(questions, {
        onCancel: () => {
          console.log('Operation cancelled.');
          process.exit(0);
        }
      });

      if (!response.appName || !response.themeColor || !response.appKey || response.debugMode === undefined || !response.logoNumber) { // Check all fields
          // This case might happen if prompts returns an empty object, e.g. on SIGINT
          console.log('Input collection failed or was cancelled.');
          process.exit(1);
      }
      appName = response.appName;
      themeColor = response.themeColor;
      appKey = response.appKey;
      debugMode = response.debugMode; // Get debug mode from prompt
      logoNumber = response.logoNumber; // Get logo number from prompt
    }

    // --- Start: Theme asset copying logic ---
    const sourceThemeDir = path.join(__dirname, '..', 'colors_theme', themeColor);
    const targetDir = path.join(__dirname, '..', 'public', 'assets', 'images');

    console.log(`\nPreparing theme assets for "${themeColor}"...`);
    console.log(`Source: ${sourceThemeDir}`);
    console.log(`Target: ${targetDir}`);

    try {
      // console.log(`Ensuring target directory exists and is empty: ${targetDir}`);
      // await fsExtra.emptyDir(targetDir); // Removed this line
      console.log(`Copying assets from ${sourceThemeDir} to ${targetDir} (overwriting existing)...`);
      await fsExtra.copy(sourceThemeDir, targetDir, { overwrite: true }); // Ensure overwrite is explicitly true (default)
      console.log('Theme assets merged successfully.');
    } catch (copyError) {
      console.error(`Error copying theme assets: ${copyError}`);
      process.exit(1); // Exit if assets can't be copied
    }
    // --- End: Theme asset copying logic ---

    // --- Start: Logo copying logic ---
    const sourceLogoPath = path.join(__dirname, '..', 'logo', `${logoNumber}.png`);
    const targetLogoPath = path.join(__dirname, '..', 'public', 'assets', 'images', 'logo.png');

    console.log(`\nPreparing logo asset (logo ${logoNumber})...`);
    console.log(`Source: ${sourceLogoPath}`);
    console.log(`Target: ${targetLogoPath}`);

    try {
      // Check if source logo file exists
      const logoExists = await fsExtra.pathExists(sourceLogoPath);
      if (!logoExists) {
        console.error(`Error: Logo file ${sourceLogoPath} does not exist.`);
        process.exit(1);
      }

      // Ensure target directory exists
      await fsExtra.ensureDir(path.dirname(targetLogoPath));

      // Copy the logo file
      console.log(`Copying logo from ${sourceLogoPath} to ${targetLogoPath}...`);
      await fsExtra.copy(sourceLogoPath, targetLogoPath, { overwrite: true });
      console.log('Logo copied successfully.');
    } catch (logoError) {
      console.error(`Error copying logo: ${logoError}`);
      process.exit(1); // Exit if logo can't be copied
    }
    // --- End: Logo copying logic ---

    // Determine the actual command string
    let commandToRun;
    if (commandArg === 'dev') {
      commandToRun = 'vite --host';
    } else { // commandArg === 'build'
      commandToRun = 'vite build';
    }

    console.log(`\nRunning: ${commandToRun} (for app "${appName}", key "${appKey}" with theme "${themeColor}", logo ${logoNumber}, Debug: ${debugMode})...`);

    // Run the actual command with the collected info as environment variables
    // Pass the entire command string to spawn with shell: true
    const child = spawn(commandToRun, [], { // Pass empty args array when command string includes args
      stdio: 'inherit', // Pass through stdin, stdout, stderr
      shell: true, // Use shell to resolve commands like 'npm' or 'vite' correctly
      env: {
        ...process.env, // Inherit existing environment variables
        // Ensure these are set for the vite process, regardless of whether they came from env or prompts
        VITE_APP_NAME: appName,
        VITE_THEME_COLOR: themeColor,
        VITE_APP_KEY: appKey,
        VITE_DEBUG: String(debugMode), // Pass debug mode as string 'true' or 'false'
        VITE_LOGO_NUMBER: String(logoNumber) // Pass logo number as string
      },
    });

    child.on('close', (code) => {
      process.exit(code);
    });

    child.on('error', (err) => {
        console.error(`Failed to start command: ${commandToRun}`, err);
        process.exit(1);
    });

  } catch (error) {
    console.error('An error occurred:', error);
    process.exit(1);
  }
})(); 