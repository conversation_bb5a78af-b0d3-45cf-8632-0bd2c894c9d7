{"name": "maomi-app", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "node scripts/prompt-config.js dev", "build": "node scripts/prompt-config.js build", "lint": "eslint .", "preview": "vite preview", "generate-favicon": "node scripts/generate-favicon.js"}, "dependencies": {"@tanstack/react-query": "^5.72.0", "@types/dplayer": "^1.25.5", "@types/qrcode.react": "^1.0.5", "@types/react-helmet": "^6.1.11", "antd-mobile": "^5.38.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "dplayer": "^1.27.1", "framer-motion": "^12.9.4", "hls.js": "^1.5.19", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-router": "^7.1.1", "react-waypoint": "^10.3.0", "universal-cookie": "^8.0.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/crypto-js": "^4.2.2", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "fs-extra": "^11.3.0", "globals": "^15.14.0", "less": "^4.2.1", "postcss": "^8.4.49", "prompts": "^2.4.2", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5"}}