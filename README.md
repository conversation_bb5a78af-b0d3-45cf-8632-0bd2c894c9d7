# React + TypeScript + Vite

This template provides a minimal setup to get <PERSON><PERSON> working in Vite with HMR and some ESLint rules.

Currently, two official plugins are available:

- [@vitejs/plugin-react](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react/README.md) uses [Babel](https://babeljs.io/) for Fast Refresh
- [@vitejs/plugin-react-swc](https://github.com/vitejs/vite-plugin-react-swc) uses [SWC](https://swc.rs/) for Fast Refresh

## Expanding the ESLint configuration

If you are developing a production application, we recommend updating the configuration to enable type aware lint rules:

- Configure the top-level `parserOptions` property like this:

```js
export default tseslint.config({
  languageOptions: {
    // other options...
    parserOptions: {
      project: ["./tsconfig.node.json", "./tsconfig.app.json"],
      tsconfigRootDir: import.meta.dirname,
    },
  },
});
```

- Replace `tseslint.configs.recommended` to `tseslint.configs.recommendedTypeChecked` or `tseslint.configs.strictTypeChecked`
- Optionally add `...tseslint.configs.stylisticTypeChecked`
- Install [eslint-plugin-react](https://github.com/jsx-eslint/eslint-plugin-react) and update the config:

```js
// eslint.config.js
import react from "eslint-plugin-react";

export default tseslint.config({
  // Set the react version
  settings: { react: { version: "18.3" } },
  plugins: {
    // Add the react plugin
    react,
  },
  rules: {
    // other rules...
    // Enable its recommended rules
    ...react.configs.recommended.rules,
    ...react.configs["jsx-runtime"].rules,
  },
});
```

# React Query Implementation

This project uses [TanStack Query](https://tanstack.com/query/latest) (formerly React Query) for data fetching, caching, and state management.

## Overview

We've migrated from direct service function calls to React Query hooks. This provides several benefits:

- Automatic caching and refetching
- Loading and error states
- Deduplication of requests
- Background updates
- Pagination and infinite scrolling support
- Optimistic UI updates

## How to Use

### Query Provider

The application is wrapped with the QueryProvider in `main.tsx`:

```tsx
import { QueryProvider } from "./providers/QueryProvider";

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <QueryProvider>
      <App />
    </QueryProvider>
  </StrictMode>
);
```

### Using Query Hooks

Replace direct service API calls with the appropriate hooks:

#### Before:

```tsx
import { authService } from "../../services/auth";

// Inside component
const handleLogin = async () => {
  try {
    setLoading(true);
    const response = await authService.login(username, password);
    // Handle response
  } catch (error) {
    // Handle error
  } finally {
    setLoading(false);
  }
};
```

#### After:

```tsx
import { useLogin } from "../../hooks/useAuth";

// Inside component
const loginMutation = useLogin();

const handleLogin = async () => {
  try {
    const response = await loginMutation.mutateAsync({
      username,
      password,
    });
    // Handle response
  } catch (error) {
    // Error is automatically handled
  }
};
```

### Available Hooks

#### Auth Hooks

- `useLogin()` - For handling user login
- `useRegister()` - For handling user registration
- `useSendResetCode()` - For sending password reset codes
- `useUpdatePassword()` - For updating user passwords
- `useSendVerificationCode()` - For sending verification codes
- `useBindPhone()` - For binding a phone number to an account
- `useUserInfo()` - For retrieving user information

#### App Hooks

- `useAppConfig()` - For fetching app configuration
- `useMenus()` - For retrieving application menus
- `useBanners()` - For fetching banner information
- `useNotices()` - For retrieving notices
- `useScrollingMessages()` - For scrolling messages
- `useOrdersList()` - For order listings
- `useCreateOrder()` - For creating new orders
- `useMemberTopics()` - For member topics

#### Content Hooks

- `useHomeVideos()` - For fetching home videos
- `useVideosList()` - For listing videos
- `useVideoDetails()` - For fetching video details
- `useTopicsList()` - For listing topics
- `useTopicDetails()` - For fetching topic details
- `useProductsList()` - For listing products
- `useImportVideo()` - For importing videos
- `useImportTopic()` - For importing topics

## Query vs Mutation

- **Queries** are for GET operations (retrieving data) and are cached
- **Mutations** are for POST/PUT/DELETE operations (changing data) and are not cached

## Best Practices

1. Always use the hook that's specific to the operation you're performing.
2. For queries that depend on variables, include those variables in the query key.
3. Use the `enabled` parameter to control when queries should run.
4. Use `onSuccess` and `onError` callbacks in mutations for side effects after the operation completes.
5. Use `refetch` for manual refetching when needed.
