import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react";
import packageJson from "./package.json";
// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // Load env vars based on mode (development, production)
  // Adjust the path ('../') if your vite config is not in the root
  const env = loadEnv(mode, process.cwd(), "");

  const isDebug = env.VITE_DEBUG === "true";
  console.log(`Build mode: ${mode}, Debug enabled: ${isDebug}`); // Log mode and debug status

  return {
    plugins: [react()],
    resolve: {
      alias: {
        "@": "/src",
        "@hooks": "/src/hooks",
        "@utils": "/src/utils",
        "@components": "/src/components",
      },
    },
    define: {
      "process.env.APP_VERSION": JSON.stringify(packageJson.version),
      // Define VITE_DEBUG for client-side use if needed (optional)
      "process.env.VITE_DEBUG": JSON.stringify(isDebug),
    },
    build: {
      terserOptions: {
        compress: {
          // Drop console logs only if not in debug mode
          drop_console: !isDebug,
        },
      },
    },
  };
});
