<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Endpoint & JSON Payload Extractor</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }
        #html-input {
            width: 100%;
            height: 200px;
            padding: 8px;
            margin-bottom: 10px;
            font-family: monospace;
        }
        .selector-input {
            width: 70%;
            padding: 8px;
            margin-right: 10px;
            margin-bottom: 5px;
        }
        button {
            padding: 8px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        #results {
            margin-top: 20px;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            min-height: 100px;
        }
        #status {
            color: #666;
            font-style: italic;
            margin-bottom: 10px;
        }
        pre {
            white-space: pre-wrap;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>API Endpoint & JSON Payload Extractor</h1>
    <p>Paste HTML content below to extract API endpoints and their JSON payloads:</p>
    
    <div>
        <textarea id="html-input" placeholder="Paste HTML content here..."></textarea>
    </div>
    
    <div style="margin-bottom: 15px;">
        <div>
            <label for="endpoint-selector">Endpoint Selector:</label><br>
            <input type="text" id="endpoint-selector" class="selector-input" value="div.sl-flex-1.sl-font-semibold">
        </div>
        <div>
            <label for="json-selector">JSON Payload Selector:</label><br>
            <input type="text" id="json-selector" class="selector-input" value="div.code-editor.language-json">
        </div>
        <button onclick="extractContent()">Extract Content</button>
        <button onclick="extractFromText()">Extract From Plain Text</button>
    </div>
    
    <div id="status">Ready</div>
    <div id="results"></div>

    <script>
        // Function to extract text content
        function extractContent() {
            const htmlContent = document.getElementById('html-input').value;
            const endpointSelector = document.getElementById('endpoint-selector').value || 'div.sl-flex-1.sl-font-semibold';
            const jsonSelector = document.getElementById('json-selector').value || 'div.code-editor.language-json';
            
            const statusElement = document.getElementById('status');
            const resultsElement = document.getElementById('results');
            
            if (!htmlContent) {
                statusElement.textContent = 'Please paste HTML content first';
                return;
            }
            
            try {
                statusElement.textContent = 'Processing...';
                
                // Parse the HTML
                const parser = new DOMParser();
                const doc = parser.parseFromString(htmlContent, 'text/html');
                
                // Query for endpoint elements
                const endpointElements = doc.querySelectorAll(endpointSelector);
                const endpoints = [];
                
                // Extract endpoints
                endpointElements.forEach((element, index) => {
                    endpoints.push({
                        index: index + 1,
                        text: element.textContent.trim(),
                        id: element.id || ''
                    });
                });
                
                // Query for JSON payload elements
                const jsonElements = doc.querySelectorAll(jsonSelector);
                const jsonPayloads = [];
                
                // Extract JSON payloads
                jsonElements.forEach((element, index) => {
                    const id = element.id || '';
                    let endpoint = '';
                    
                    // Try to find corresponding endpoint from the endpoints array
                    if (index < endpoints.length) {
                        endpoint = endpoints[index].text;
                    }
                    
                    // If no endpoint found or as fallback, try to extract from id
                    if (!endpoint && id) {
                        // Extract method and path from id like "json-body-POSTapi-login"
                        const match = id.match(/json-body-([A-Z]+)api-(.+)/);
                        if (match && match.length >= 3) {
                            const method = match[1]; // POST, GET, etc.
                            const path = match[2].replace(/-/g, '/'); // Convert hyphens to slashes
                            endpoint = `${method} /api/${path}`;
                        }
                    }
                    
                    try {
                        // Clean up the text content to get valid JSON
                        let jsonText = element.textContent
                            .replace(/\n\s*/g, ' ')    // Replace newlines with spaces
                            .replace(/\s+/g, ' ')      // Normalize whitespace
                            .trim();                   // Trim outer whitespace
                        
                        // Try to parse as JSON to validate and format
                        const jsonObj = JSON.parse(jsonText);
                        jsonPayloads.push({
                            id: id,
                            endpoint: endpoint,
                            text: JSON.stringify(jsonObj, null, 2), // Pretty-print the JSON
                            raw: jsonText
                        });
                    } catch (jsonError) {
                        // If JSON parsing fails, clean up the text manually
                        const cleanedText = element.textContent
                            .replace(/\s+/g, ' ')
                            .trim();
                            
                        jsonPayloads.push({
                            id: id,
                            endpoint: endpoint,
                            text: cleanedText,
                            raw: element.textContent.trim(),
                            error: jsonError.message
                        });
                    }
                });
                
                // Display results
                if (jsonPayloads.length === 0) {
                    resultsElement.innerHTML = `<p>No JSON elements found matching the selector: "${jsonSelector}"</p>`;
                } else {
                    let plainTextOutput = `JSON Payloads (${jsonPayloads.length} found):\n\n`;
                    
                    jsonPayloads.forEach((json, index) => {
                        if (json.endpoint) {
                            plainTextOutput += `${index + 1}. Endpoint: ${json.endpoint}\n`;
                        } else {
                            plainTextOutput += `${index + 1}. ID: ${json.id}\n`;
                        }
                        
                        plainTextOutput += `${json.text}\n\n`;
                    });
                    
                    resultsElement.innerHTML = `<pre>${plainTextOutput}</pre>`;
                }
                
                statusElement.textContent = 'Extraction complete';
                
            } catch (error) {
                console.error('Error:', error);
                statusElement.textContent = 'Error: ' + error.message;
                resultsElement.innerHTML = '<p>There was an error processing the HTML content.</p>';
            }
        }

        // Function to extract from plain text input
        function extractFromText() {
            const textContent = document.getElementById('html-input').value;
            const statusElement = document.getElementById('status');
            const resultsElement = document.getElementById('results');
            
            if (!textContent) {
                statusElement.textContent = 'Please paste content first';
                return;
            }
            
            try {
                statusElement.textContent = 'Processing...';
                
                // Parse the text content line by line
                const lines = textContent.split('\n');
                const jsonPayloads = [];
                
                let currentEndpoint = '';
                let currentJson = '';
                let isCollectingJson = false;
                
                for (let i = 0; i < lines.length; i++) {
                    const line = lines[i].trim();
                    
                    // Check for endpoint pattern
                    const endpointMatch = line.match(/(\d+\.\s*Endpoint:\s*)(.+)/i);
                    if (endpointMatch) {
                        // If we were collecting JSON, save it
                        if (isCollectingJson && currentEndpoint && currentJson) {
                            try {
                                const jsonObj = JSON.parse(currentJson);
                                jsonPayloads.push({
                                    endpoint: currentEndpoint,
                                    text: JSON.stringify(jsonObj, null, 2)
                                });
                            } catch (err) {
                                jsonPayloads.push({
                                    endpoint: currentEndpoint,
                                    text: currentJson,
                                    error: err.message
                                });
                            }
                        }
                        
                        // Start new collection
                        currentEndpoint = endpointMatch[2].trim();
                        currentJson = '';
                        isCollectingJson = true;
                        continue;
                    }
                    
                    // If we're collecting JSON and line isn't empty, add to current JSON
                    if (isCollectingJson && line) {
                        currentJson += line + '\n';
                    }
                    
                    // If this is an empty line and we have JSON content, we might be at the end of a JSON block
                    if (!line && isCollectingJson && currentJson.trim()) {
                        // Check if the next line is another endpoint or we're at the end
                        if (i+1 >= lines.length || lines[i+1].match(/\d+\.\s*Endpoint:/i)) {
                            try {
                                const jsonObj = JSON.parse(currentJson);
                                jsonPayloads.push({
                                    endpoint: currentEndpoint,
                                    text: JSON.stringify(jsonObj, null, 2)
                                });
                            } catch (err) {
                                jsonPayloads.push({
                                    endpoint: currentEndpoint,
                                    text: currentJson,
                                    error: err.message
                                });
                            }
                            
                            isCollectingJson = false;
                            currentJson = '';
                        }
                    }
                }
                
                // Handle the last JSON if there is one
                if (isCollectingJson && currentEndpoint && currentJson.trim()) {
                    try {
                        const jsonObj = JSON.parse(currentJson);
                        jsonPayloads.push({
                            endpoint: currentEndpoint,
                            text: JSON.stringify(jsonObj, null, 2)
                        });
                    } catch (err) {
                        jsonPayloads.push({
                            endpoint: currentEndpoint,
                            text: currentJson,
                            error: err.message
                        });
                    }
                }
                
                // Display results
                if (jsonPayloads.length === 0) {
                    resultsElement.innerHTML = `<p>No API endpoints or JSON payloads found in the text.</p>`;
                } else {
                    let plainTextOutput = `JSON Payloads (${jsonPayloads.length} found):\n\n`;
                    
                    jsonPayloads.forEach((json, index) => {
                        plainTextOutput += `${index + 1}. Endpoint: ${json.endpoint}\n`;
                        plainTextOutput += `${json.text}\n\n`;
                    });
                    
                    resultsElement.innerHTML = `<pre>${plainTextOutput}</pre>`;
                }
                
                statusElement.textContent = 'Extraction complete';
                
            } catch (error) {
                console.error('Error:', error);
                statusElement.textContent = 'Error: ' + error.message;
                resultsElement.innerHTML = '<p>There was an error processing the text content.</p>';
            }
        }
    </script>
</body>
</html>