@tailwind base;
@tailwind components;
@tailwind utilities;


:root:root {
  --adm-font-family: "PingFang SC", "SF Pro SC", "SF Pro Text", "SF Pro Icons", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}
:root {
   /* --text-color-primary: 231 119 135; */
  --text-color-primary: 87 183 138;
  /* --text-color-primary-dark: 214 76 101; */
  --text-color-primary-dark: 83 156 132;
  --text-color-svip: 152 110 66;
  --text-color-orange: 255 128 0;
  --text-color-blue: 60 167 251;
  --text-color-gold: 221 188 128;
  /* --icon-color-primary: 255 142 157; */
  --icon-color-primary: 87 183 138;
  --icon-color-gold: 221 188 128;
  /* --gradient-color-primary1: 251 142 183; */
  --gradient-color-primary1: 74 176 135;
  /* --gradient-color-primary2: 234 166 199; */
  --gradient-color-primary2: 177 232 161;
  --gradient-color-primary3: 217 191 217;
  --disabled-grey: 169 169 169; /* #A9A9A9  */
  --disabled-icon-grey: 112 112 112; /* #707070  */
  --unselected-button-color: 249 245 255; /* #F9F5FF  */
}

/* Default Theme */
:root:not(.vip-theme) {
  --current-gradient-start: var(--gradient-color-primary1);
  --current-gradient-end: var(--gradient-color-primary2);
}

img {
  -webkit-user-drag: none;
}

.adm-tab-bar-wrap {
  height: 60px;
  overflow: visible !important;
}

/* Hide tab masks for main menu tabs */
.mainMenuTabs .adm-tabs-header-mask {
  display: none !important;
}

.bg-textColorGold {
  background-color: rgb(var(--text-color-gold));
}

/* Base button styles */
.disabledBtn {
  color: #fff !important;
  border-radius: 50px !important;
  line-height: 32px !important;
  background-color: rgb(var(--disabled-icon-grey)) !important;
}

.primayBtn {
  color: #fff !important;
  border-radius: 50px !important;
  line-height: 32px !important;
  background-color: rgb(var(--text-color-primary)) !important;
}

.primayIconBtn {
  color: #fff !important;
  border-radius: 50px !important;
  line-height: 32px !important;
  background-color: rgb(var(--icon-color-primary)) !important;
}
