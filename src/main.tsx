import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import App from "./App.tsx";
import { QueryProvider } from "./providers/QueryProvider";

import Hls from "hls.js";

const w = window as any;
w.Hls = Hls;

// Disable right-click context menu ONLY if not in debug mode
// Vite replaces process.env.VITE_DEBUG with the stringified value during build
const isDebug = process.env.VITE_DEBUG;
console.log("isDebug", isDebug);
if (!isDebug) {
  document.addEventListener("contextmenu", (event) => event.preventDefault());
}

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <QueryProvider>
      <App />
    </QueryProvider>
  </StrictMode>
);
