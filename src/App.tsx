import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { RouterProvider } from "react-router";
import { router } from "./router";
import { UserProvider } from "./contexts/UserContext";
import AppInit from "./components/AppInit";
import { MenusProvider } from "./contexts/MenusContext";
import AnnouncementModalController from "./components/AnnouncementModal";
import { useState, useEffect } from "react";
import { useAppConfig } from "./hooks/useApp";
import SplashScreen from "./components/SplashScreen";

import "./App.less";
import { ThemeProvider } from "./providers/ThemeProvider";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

function App() {
  const { isLoading: isConfigLoading, isError: isConfigError } = useAppConfig();
  const [showSplash, setShowSplash] = useState(true);

  useEffect(() => {
    if (!isConfigLoading) {
      const timer = setTimeout(() => {
        setShowSplash(false);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [isConfigLoading]);

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        {showSplash ? (
          <SplashScreen />
        ) : (
          <MenusProvider>
            <UserProvider>
              <AppInit>
                <RouterProvider router={router} />
                <AnnouncementModalController />
              </AppInit>
            </UserProvider>
          </MenusProvider>
        )}
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;
