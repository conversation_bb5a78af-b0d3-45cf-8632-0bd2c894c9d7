import axios, {
  AxiosError,
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
} from "axios";
import { base64Sign, encrypt, decrypt } from "./utils";
import Cookies from "universal-cookie";

const cookies = new Cookies();

const baseURL = "https://merchantapp.3y16zngg.com/api";

const request: AxiosInstance = axios.create({
  baseURL,
  timeout: 10e4,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor
request.interceptors.request.use(
  (config) => {
    // Get access token from localStorage
    const accessToken = localStorage.getItem("access_token");

    // If access token exists, add it to the Authorization header
    if (accessToken) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    }

    return config;
  },
  (error: AxiosError) => {
    return Promise.reject(error);
  }
);

// 请求拦截器
request.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 拦截请求配置，进行个性化处理。
    const tokenName = localStorage.getItem("token_name") || "";
    const token = cookies.get(tokenName);

    if (token) {
      config.headers[tokenName] = token;
    }

    let payload = {} as any;
    const data = config?.data;
    const method = config.method?.toLowerCase();

    payload["system"] = 1;
    payload["timestamp"] = new Date().getTime();
    payload = { ...payload, ...data };
    console.info(`-- ${config.url} --`, payload);
    payload["encode_sign"] = base64Sign(payload);
    const encryptedPayload = encrypt(JSON.stringify(payload));

    if (method === "get") {
      // For GET requests, add the payload as query parameters
      config.params = {
        "post-data": encryptedPayload,
      };
    } else {
      // For other methods (POST, PUT, etc.), add the payload to request body
      config.data = {
        "post-data": encryptedPayload,
      };
    }

    if (config.baseURL) {
      config.headers["suffix"] = 123456;
      config.headers["Content-Type"] = "application/json";
    }

    return config;
  },
  (error: AxiosError) => {
    //对请求错误做些什么
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse) => {
    const { data } = response;

    if (data.code === 401) {
      // Clear access token on unauthorized
      localStorage.removeItem("access_token");
      return (window.location.href = "/login");
    }

    if (data.code !== 200) {
      //   message.error(data.message);
    }
    // console.log("response", response);
    if (response.data.suffix) {
      let __data = decrypt(response.data.data, response.data.suffix);
      __data = JSON.parse(__data);
      response.data.data = __data;
    } else {
      response.data.data = data;
    }
    return data;
  },
  (error: AxiosError) => {
    //对请求错误做些什么
    return Promise.reject(error);
  }
);

export const http = {
  post<T>(url: string, data?: object, config?: AxiosRequestConfig): Promise<T> {
    return request.post(url, data, config);
  },
  get<T>(
    url: string,
    params?: object,
    config?: AxiosRequestConfig
  ): Promise<T> {
    return request.get(url, { params, ...config });
  },
};

export default request;
