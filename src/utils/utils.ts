import CryptoJS from "crypto-js";
import dayjs from "dayjs";

const KEY = "SWRUSnEwSGtscHVJNm11OGlCJU9PQCF2ZF40SyZ1WFc=";
const IV = "JDB2QGtySDdWMg==";
const SIGN_KEY = "JkI2OG1AJXpnMzJfJXUqdkhVbEU0V2tTJjFKNiUleG1VQGZO";
const SUFFIX = 123456;
const SECRET_KEY = "D7hGKHnWThaECaQ3ji4XyAF3MfYKJ53M";
let abortController = new AbortController();
const objKeySort = (arys: any) => {
  const newObj: any = {};
  const newkey = Object.keys(arys).sort();
  for (let i = 0; i < newkey.length; i++) {
    newObj[newkey[i]] = arys[newkey[i]];
  }
  return newObj;
};

export const base64decoder = (Context: any) => {
  const tmp = CryptoJS.enc.Base64.parse(Context); // encryptedWord via Base64.parse()
  return CryptoJS.enc.Utf8.stringify(tmp);
};

export const base64Sign = (data: any) => {
  data = objKeySort(data);
  let pre_sign = "";
  for (const i in data) {
    pre_sign += i + "=" + data[i] + "&";
  }
  const key = base64decoder(SIGN_KEY);
  pre_sign += key;
  return CryptoJS.MD5(pre_sign).toString();
};

export const encrypt = (data: any, suffix = SUFFIX) => {
  let new_key: any = base64decoder(KEY);
  let new_iv: any = base64decoder(IV);

  new_iv = CryptoJS.enc.Utf8.parse(new_iv + suffix);
  new_key = CryptoJS.enc.Utf8.parse(new_key);

  const encrypted = CryptoJS.AES.encrypt(data, new_key, {
    iv: new_iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
    formatter: CryptoJS.format.OpenSSL,
  });
  return encrypted.toString();
};

export const decrypt = (data: any, suffix = SUFFIX) => {
  let new_key: any = base64decoder(KEY);
  let new_iv: any = base64decoder(IV);

  new_iv = CryptoJS.enc.Utf8.parse(new_iv + suffix);
  new_key = CryptoJS.enc.Utf8.parse(new_key);

  const decrypted = CryptoJS.AES.decrypt(data, new_key, {
    iv: new_iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
    formatter: CryptoJS.format.OpenSSL,
  });
  return decrypted.toString(CryptoJS.enc.Utf8);
};

export const fetchData = async (url: string) => {
  try {
    const signal = abortController.signal;
    const response = await fetch(url, { signal });
    const data = await response.text();
    return data;
  } catch {
    return "";
  }
};

export const imgDecrypt = (data: any) => {
  try {
    const asc_key = "jeH3O1VX";
    const base_lv = "nHnsU4cX";
    const tmpiv = CryptoJS.enc.Utf8.parse(base_lv);
    const keyHex = CryptoJS.enc.Utf8.parse(asc_key);
    const decrypted = CryptoJS.DES.decrypt(data, keyHex, {
      iv: tmpiv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
      formatter: CryptoJS.format.OpenSSL,
    });
    return decrypted.toString(CryptoJS.enc.Utf8);
  } catch {
    return "";
  }
};

export const fmtDateSince = (date: any) => {
  // If date is a number (Unix timestamp), convert it with dayjs.unix()
  const dateObj = typeof date === "number" ? dayjs.unix(date) : dayjs(date);
  const seconds = Math.floor((+new Date() - dateObj.valueOf()) / 1000);

  let interval = seconds / 31536000;
  // years
  if (interval > 1) {
    return dateObj.format("YYYY-MM-DD");
  }
  // months
  interval = seconds / 2592000;
  if (interval > 1) {
    //   return dayjs(date).format('YYYY-MM-DD')
    return Math.floor(interval) + " 个月前";
  }

  // days
  interval = seconds / 86400;
  // if (interval > 7) {
  //   return dayjs(date).format('YYYY-MM-DD')
  // }
  // if (interval > 1 && interval <= 7) {
  if (interval > 1) {
    return Math.floor(interval) + " 天前";
  }
  // hours
  interval = seconds / 3600;
  if (interval > 1) {
    return Math.floor(interval) + " 小时前";
  }
  // minutes
  interval = seconds / 60;
  if (interval > 1) {
    return Math.floor(interval) + " 分钟前";
  }
  //seconds
  return Math.floor(seconds) + " 秒";
};

export const toFmt = (t: any) => {
  if (!t) return t;
  return typeof t === "number"
    ? dayjs.unix(t).format("YYYY-MM-DD")
    : dayjs(t).format("YYYY-MM-DD");
};

export function secondsToHms(time: string) {
  const d = Number(time);
  const h = Math.floor(d / 3600);
  const m = Math.floor((d % 3600) / 60);
  const s = Math.floor((d % 3600) % 60);

  // let hDisplay = h > 0 ? h + (h == 1 ? ' hour, ' : ' hours, ') : '';
  // let mDisplay = m > 0 ? m + (m == 1 ? ' minute, ' : ' minutes, ') : '';
  // let sDisplay = s > 0 ? s + (s == 1 ? ' second' : ' seconds') : '';

  const hDisplay = h > 0 ? h + ":" : "00:";
  const mDisplay = m > 0 ? m + ":" : "00:";
  const sDisplay = s > 0 ? s + "" : ":";
  return hDisplay + mDisplay + sDisplay;
}

export const isMobile = () => {
  return window.matchMedia("only screen and (max-width: 700px)").matches;
};

export const addImgKeyParam = (url: string) => {
  let secret_key = SECRET_KEY;
  let currentTime = Math.floor(Date.now() / 1000); // Current time in seconds
  let storedTime = sessionStorage.getItem("timestamp"); // Get the stored timestamp from sessionStorage

  let time; // Declare the time variable

  // If there is a stored time and 5 minutes haven't passed, reuse the stored time
  if (storedTime && currentTime - parseInt(storedTime) < 300) {
    time = parseInt(storedTime) + 300; // Use the stored timestamp and add 300 seconds
  } else {
    // If no stored time or more than 5 minutes have passed, generate a new timestamp
    time = currentTime + 300; // Add 300 seconds to the current time
    sessionStorage.setItem("timestamp", currentTime.toString()); // Store the new timestamp in sessionStorage
  }

  // let hexTime = time.toString(16); // 转换为十六进制字符串
  // let uri = "/xmmvip/xmmvip/e73172371207066dc2d5ad72f2__289916/e73172371207066dc2d5ad72f2__289916_thumb_5903.jpg"

  // The URI for which we're generating the key
  let uri = url;

  // Concatenate secret_key, uri, and time (decimal) for the MD5 hash
  let paramToAppend = secret_key + uri + time; // 确保这里是十六进制

  let key = CryptoJS.MD5(paramToAppend).toString(); // 使用CryptoJS计算MD5

  return "?wsSecret=" + key + "&wsTime=" + time; // 使用十六进制时间戳
};

export const addVidKeyParam2 = (url: string) => {
  let secret_key = SECRET_KEY;
  let currentTime = Math.floor(Date.now() / 1000); // Current time in seconds
  let storedTime = sessionStorage.getItem("timestamp"); // Get the stored timestamp from sessionStorage

  let time; // Declare the time variable

  // If there is a stored time and 5 minutes haven't passed, reuse the stored time
  if (storedTime && currentTime - parseInt(storedTime) < 300) {
    time = parseInt(storedTime) + 300; // Use the stored timestamp and add 300 seconds
  } else {
    // If no stored time or more than 5 minutes have passed, generate a new timestamp
    time = currentTime + 300; // Add 300 seconds to the current time
    sessionStorage.setItem("timestamp", currentTime.toString()); // Store the new timestamp in sessionStorage
  }

  // Convert the time to a hexadecimal string

  // The URI for which we're generating the key
  let uri = url;

  // Concatenate secret_key, uri, and time (decimal) for the MD5 hash
  let paramToAppend = secret_key + uri + time; // Time in decimal

  // Calculate the MD5 hash using CryptoJS
  let key = CryptoJS.MD5(paramToAppend).toString();

  // Return the URL with wsSecret (the MD5 hash) and wsTime (in decimal)
  return "?wsSecret=" + key + "&wsTime=" + time; // wsTime is the time in seconds (decimal)
};
