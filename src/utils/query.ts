import { http } from "./request";

// Common interface for API responses
export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

// Error handler for mutations
export const handleMutationError = (error: unknown): string => {
  if (error instanceof Error) {
    return error.message;
  }
  return "An unknown error occurred";
};

// Base function for POST requests
export const postRequest = async <T, P extends object = object>(
  url: string,
  payload?: P
): Promise<T> => {
  const response = await http.post<ApiResponse<T>>(url, payload);
  return response.data;
};

// Base function for GET requests
export const getRequest = async <T, P extends object = object>(
  url: string,
  payload?: P
): Promise<T> => {
  const response = await http.get<ApiResponse<T>>(url, { data: payload });
  return response.data;
};
