export interface Topic {
  id: number;
  topic_uid: string;
  title: string;
  description: string;
  download_url: string | null;
  cover: string;
  gif_images: string[];
  price: string;
  discount: string;
  status: number;
  created_at: number;
  updated_at: number;
  deleted_at: number | null;
}

export interface MemberTopic {
  id: number;
  application_id: number;
  product_id: number;
  topic_uid: string;
  title: string;
  cover: string;
  member_id: number;
  payment_gateway_id: number;
  external_order_no: string | null;
  order_no: string;
  status: number;
  type: number;
  amount: string;
  remark: string | null;
  paid_at: string | null;
  expiration_date: string;
  created_at: number;
  updated_at: number;
}

export interface MemberTopicsResponse {
  app_member_topic: MemberTopic[];
}

export interface UserInfo {
  id: number;
  public_id: string;
  application_id: number;
  name: string | null;
  username: string;
  email: string | null;
  status: number;
  status_label: string;
  last_login_at: string | null;
  last_login_ip: string | null;
  expired_at: string | null;
  product_id: number | null;
  created_at: number;
  updated_at: number;
  deleted_at: string | null;
}

export interface UserInfoResponse {
  data: UserInfo;
}
