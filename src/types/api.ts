// API payload types
// App config
export interface AppConfigPayload {
  app: string;
}

// Auth
export interface LoginPayload {
  app: string;
  username: string;
  phone?: string | null;
  country_code?: string | null;
  password: string;
}

export interface PhoneLoginPayload {
  app: string;
  country_code: string;
  phone: string;
  password: string;
}

export interface RegisterPayload {
  app: string;
  username: string;
  password: string;
  password_confirmation: string;
  invite_code?: string;
}

export interface SendResetCodePayload {
  app: string;
  country_code: string;
  phone: string;
}

export interface UpdatePasswordPayload {
  old_password: string;
  new_password: string;
  password_confirmation: string;
}

export interface SendVerificationCodePayload {
  app: string;
  country_code: string;
  phone: string;
}

export interface BindPhonePayload {
  app: string;
  country_code: string;
  phone: string;
  code: string;
}

export interface ResetForgotPasswordPayload {
  app: string;
  country_code: string;
  phone: string;
  code: string;
  password: string;
  password_confirmation: string;
}

// Menus
export interface MenuItemChild {
  id: number;
  title: string;
  parent_id: number;
  order: number;
  is_vip: number;
  video_category_id: number | null;
  application_id: number;
  created_at: string;
  updated_at: string;
}

export interface MenuItem {
  id: number;
  title: string;
  parent_id: number;
  order: number;
  is_vip: number;
  video_category_id: number | null;
  application_id: number;
  created_at: string;
  updated_at: string;
  child: MenuItemChild[];
}

export interface BenefitItem {
  id: number;
  title: string;
  parent_id: number;
  order: number;
  img: string;
  url: string;
  created_at: string;
  updated_at: string;
  child?: BenefitItem[];
}

export interface MenusResponse {
  appMenus: {
    normal: MenuItem[];
    vip: MenuItem[];
    topic: MenuItem[];
    benefits: BenefitItem[];
  };
}

// Notices
export interface NoticesPayload {
  app: string;
  type?: number;
}

// Orders
export interface OrdersListPayload {
  app: string;
  page?: number;
  limit?: number;
}

export interface CreateOrderPayload {
  app: string;
  product_id: number;
  payment_gateway_id: number;
  type: number;
}

export interface AppOrder {
  id: number;
  application_id: number;
  product_id: number;
  product_name: string;
  member_id: number;
  payment_gateway_id: number;
  order_no: string;
  status: number;
  type: number;
  amount: string;
  remark: string | null;
  expiration_date: string;
  created_at: number;
  updated_at: number;
}

export interface OrdersListResponse {
  application_orders: AppOrder[];
}

export interface MemberTopicsListPayload {
  app: string;
  page?: number;
  limit?: number;
}

// Content - Videos
export interface VideosIndexPayload {
  app: string;
  menu_id?: number;
}

export interface VideosListPayload {
  app: string;
  menu_id?: number;
  type?: string;
  page?: number;
  limit?: number;
  category_id?: number;
}

export interface VideoDetailsPayload {
  app: string;
  video_id: number;
}

// Video related interfaces and types
export interface VideosRelatedPayload {
  app: string;
  category_id: number;
}

export interface VideosRelatedResponse {
  code: number;
  message: string;
  videos: Video[][];
}

// Content - Topics
export interface TopicsListPayload {
  app: string;
  page?: number;
  limit?: number;
}

export interface TopicDetailsPayload {
  app: string;
  topic_id: number;
}

export interface Topic {
  id: number;
  topic_uid: string;
  title: string;
  description: string;
  cover: string;
  gif_images: string[];
  price: string;
  discount: string;
  status: number;
  created_at: number;
  updated_at: number;
  deleted_at: null | string;
  videos?: Video[];
  free_videos?: Video[];
  paid_videos?: Video[];
}

export interface AppTopic {
  id: number;
  topic_id: number;
  application_id: number;
  price: string;
  created_at: number;
  updated_at: number;
  topic: Topic;
}

export interface TopicsListResponse {
  app_topics: AppTopic[];
}

// Content - Products
export interface ProductsListPayload {
  app: string;
  page?: number;
  limit?: number;
}

// Content - Import
export interface ImportVideoPayload {
  video_uid: string;
  title: string;
  category: {
    id: number;
    name: string;
  };
  is_paid: number;
  video_url: string;
  down_url: string;
  thumb_url: string;
}

export interface ImportTopicPayload {
  topic_uid: string;
  title: string;
  description: string;
  cover: string;
  gif_images: string[];
  price: number;
  discount: number;
  videos: {
    video_uid: string;
    title: string;
    category: {
      name: string;
      id: number;
    };
    is_paid: number;
    video_url: string;
    down_url: string;
    thumb_url: string;
  }[];
}

// API response types
export interface AppConfig {
  config: {
    m3u8_url: string;
    thumb_url: string;
    customer_url: string;
    telegram_url: string;
    encrypted_image_domain: string;
    encrypted_video_domain: string;
    js_cache_domain: string;
    website_domain: string;
    mobile_domain: string;
    api_domain: string;
    primaryTheme?: string;
  };
}

export interface UserData {
  id: number;
  public_id: string;
  username: string;
  email: string | null;
  country_code: string | null;
  phone: string | null;
  status: number;
  expired_at: string | null;
  product_id: number | null;
}

export interface LoginResponseData {
  code?: number;
  message?: string;
  access_token: string;
  user: UserData;
}

// Video types
export interface Video {
  id: number;
  video_uid: string;
  title: string;
  category_id: number;
  is_paid: number;
  video_url: string;
  down_url: string;
  thumb_url: string;
  share_link?: string;
  collect: number;
  status: number;
  created_at: number;
  updated_at: number;
  deleted_at: string | null;
  pivot?: {
    topic_id: number;
    video_id: number;
  };
}

export interface PageLink {
  url: string | null;
  label: string;
  active: boolean;
}

export interface PaginatedResponse<T> {
  current_page: number;
  data: T[];
  first_page_url: string;
  from: number;
  last_page: number;
  last_page_url: string;
  links: PageLink[];
  next_page_url: string | null;
  path: string;
  per_page: number;
  prev_page_url: string | null;
  to: number;
  total: number;
}

export interface VideosListResponse {
  videos: PaginatedResponse<Video>;
}

// Video details response
export interface VideoDetailsResponse {
  video: Video;
}

export interface UserInfoResponse {
  access_token: string;
  user: {
    id: number;
    public_id: string;
    application_id: number;
    name: string | null;

    product_description: string;
    product_id: number | null;
    product_name: string;
    username: string;
    email: string | null;
    status: number;
    status_label: string;
    last_login_at: string | null;
    last_login_ip: string | null;
    expired_at: string | null;
    created_at: number;
    updated_at: number;
    deleted_at: string | null;
  };
}

export interface Banner {
  id: number;
  app_id: number;
  image_url: string;
  redirect_url: string;
  status: number;
  title: string;
  created_at: string;
  updated_at: string;
}

export interface BannersResponse {
  banners: Banner[];
}

export interface Notice {
  id: number;
  title: string;
  description: string;
  image_url: string;
  redirect_url: string;
  type: number;
  status: number;
  sorts: number;
  app_id: number;
  created_at: string;
  updated_at: string;
}

export interface NoticesResponse {
  notices: Notice[];
}

export interface ScrollingMessage {
  id: number;
  title: string;
  status: number;
  app_id: number;
  created_at: number;
  updated_at: number;
}

export interface ScrollingMessagesResponse {
  scrolling_messages: ScrollingMessage[];
}

// Product types
export interface Product {
  id: number;
  name: string;
  description: string | null;
  application_id: number;
  price: string;
  original_price: string | null;
  duration: number;
  hot: number;
  created_at: number;
  updated_at: number;
}

export interface ProductsListResponse {
  app_products: Product[];
}

export interface TopicDetailsResponse {
  app_topic: {
    id: number;
    topic_id: number;
    application_id: number;
    price: string;
    created_at: number;
    updated_at: number;
    topic: Topic;
  };
}

// Payment Gateways
export interface PaymentChannel {
  id: number;
  key: string;
  name: string;
  icon: string;
  sort: number;
  type: number;
}

export interface PaymentGateway {
  id: number;
  key: string;
  name: string;
  icon: string;
  link: string;
  list: PaymentChannel[];
}

export interface PaymentGatewayListResponse {
  payment_gateways: PaymentGateway[];
}

// App Versions
interface AppVersionDetails {
  id: number;
  app_id: number;
  version_name: string;
  version_code: number;
  download_url: string;
  changelog: string;
  is_mandatory: number; // Typically 0 or 1
  created_at: number; // Timestamp
  updated_at: number; // Timestamp
}

export interface AppVersionsResponse {
  appVersions: AppVersionDetails;
}
