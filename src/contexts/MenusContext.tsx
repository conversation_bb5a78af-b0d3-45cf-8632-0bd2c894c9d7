import { createContext, useContext, ReactNode } from "react";
import { useMenus } from "@/hooks/useApp";
import { MenusResponse } from "@/types/api";
import { UseQueryResult } from "@tanstack/react-query";

interface MenusContextType {
  data: MenusResponse | undefined;
  isLoading: boolean;
}

const MenusContext = createContext<MenusContextType | undefined>(undefined);

export const MenusProvider = ({ children }: { children: ReactNode }) => {
  const { data, isLoading } = useMenus();
  console.log("data", data);
  return (
    <MenusContext.Provider value={{ data, isLoading }}>
      {children}
    </MenusContext.Provider>
  );
};

export const useGlobalMenus = (): MenusContextType => {
  const context = useContext(MenusContext);
  if (context === undefined) {
    throw new Error("useGlobalMenus must be used within a MenusProvider");
  }
  return context;
};
