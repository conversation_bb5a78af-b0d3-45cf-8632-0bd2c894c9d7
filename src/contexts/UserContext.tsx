import {
  createContext,
  useContext,
  ReactNode,
  useState,
  useEffect,
} from "react";
import { UserInfoResponse } from "../types/api";
import { useUserInfo } from "../hooks/useAuth";

interface UserContextType {
  user: UserInfoResponse | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  logout: () => void;
  setUser: (user: UserInfoResponse | null) => void;
}

const UserContext = createContext<UserContextType>({
  user: null,
  isAuthenticated: false,
  isLoading: true,
  logout: () => {},
  setUser: () => {},
});

export const UserProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<UserInfoResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const accessToken = localStorage.getItem("access_token");
  const { data: userInfo, isLoading: isUserInfoLoading } = useUserInfo(
    !!accessToken
  );

  console.log(userInfo);

  useEffect(() => {
    if (userInfo) {
      setUser(userInfo);
      setIsLoading(false);
    } else if (!isUserInfoLoading) {
      setIsLoading(false);
    }
  }, [userInfo, isUserInfoLoading]);

  const logout = () => {
    localStorage.removeItem("access_token");
    setUser(null);
  };

  return (
    <UserContext.Provider
      value={{
        user,
        isAuthenticated: !!user,
        isLoading,
        logout,
        setUser,
      }}
    >
      {children}
    </UserContext.Provider>
  );
};

export const useUser = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
};
