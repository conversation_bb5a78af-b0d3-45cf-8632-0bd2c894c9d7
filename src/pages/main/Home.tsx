import { useState, useEffect } from "react";
import { Tabs } from "antd-mobile";
import { DynamicThemeColor } from "../../components/DynamicThemeColor";
import { PageLoader } from "../../components/common";
import { useNavigate, useLocation } from "react-router";

import TabWrapper from "../../components/TabWrapper";
import HomeContent from "./modules/HomeContent";
import { MenuItem } from "../../types/api";
import { useGlobalMenus } from "@/contexts/MenusContext";
import MainLayout from "@/layouts/MainLayout";

const Home = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const { data, isLoading } = useGlobalMenus();
  const normalMenus = data?.appMenus?.normal || [];

  // Convert normal menus to tab items
  const tabItems = normalMenus.map((menu: MenuItem) => ({
    key: menu.id.toString(),
    title: menu.title,
    menu,
  }));

  // Get initial active index from URL or default to 0
  const getInitialActiveIndex = () => {
    const params = new URLSearchParams(location.search);
    const tabParam = params.get("tab");

    if (tabParam && tabItems.length > 0) {
      const index = tabItems.findIndex((item) => item.key === tabParam);
      return index >= 0 ? index : 0;
    }
    return 0;
  };

  const [activeIndex, setActiveIndex] = useState(0);

  // Update active index based on URL params when data loads
  useEffect(() => {
    if (!isLoading && tabItems.length > 0) {
      setActiveIndex(getInitialActiveIndex());
    }
  }, [isLoading, location.search, tabItems.length]);

  // Update URL when active index changes
  const handleTabChange = (key: string) => {
    const index = tabItems.findIndex((item) => item.key === key);
    setActiveIndex(index);

    // Update URL with query parameter
    const params = new URLSearchParams(location.search);
    params.set("tab", key);

    // Preserve submenu parameter if it exists
    navigate(`?${params.toString()}`, { replace: true });
  };

  if (isLoading || tabItems.length === 0) {
    return (
      <>
        <DynamicThemeColor color="primary" />
        <PageLoader />
      </>
    );
  }

  const headerContent = (
    <Tabs
      activeKey={tabItems[activeIndex]?.key}
      onChange={handleTabChange}
      className="mainMenuTabs"
    >
      {tabItems.map((item) => (
        <Tabs.Tab title={item.title} key={item.key} />
      ))}
    </Tabs>
  );

  return (
    <TabWrapper>
      <MainLayout header={headerContent}>
        {tabItems[activeIndex] && (
          <HomeContent
            menuItem={tabItems[activeIndex].menu}
            activeTabId={tabItems[activeIndex].key}
            isFirstTab={activeIndex === 0}
          />
        )}
      </MainLayout>
    </TabWrapper>
  );
};

export default Home;
