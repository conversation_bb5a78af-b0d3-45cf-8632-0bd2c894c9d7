import { useState } from "react";
import TabWrapper from "../../components/TabWrapper";
import { useNavigate } from "react-router";
import { useTopicsList } from "@/hooks/useContent";
import Image from "@/components/common/Image";
import ImageConcat from "@/components/common/ImageConcat";
import dayjs from "dayjs";
import { fmtDateSince } from "@/utils/utils";
import { ThemeColor } from "@/components/ThemeColor";
import { PageLoader } from "@/components/common";
import MenuButton from "@/components/common/MenuButton";

const Theme = () => {
  const navigate = useNavigate();
  const { data: topicsList, isLoading } = useTopicsList();

  if (isLoading) {
    return (
      <>
        <ThemeColor color="primary" />
        <PageLoader text="正在加载专题..." />
      </>
    );
  }

  return (
    <TabWrapper>
      <ThemeColor color="primary" />
      <div className="fixed top-0 left-0 right-0 h-12 gradient-primary z-10 lg:max-w-[30vw] mx-auto">
        <div className="relative h-12 gradient-primary">
          <p className="text-center leading-[48px] text-lg text-white">
            限时专题
          </p>
          <MenuButton className="top-[40%]" />
        </div>
        {/* Banner */}
        <div className="px-5 py-3">
          <div className="relative">
            <img
              className="w-full"
              src="/assets/images/banner.png"
              alt="banner"
            />
            <div className="absolute bottom-2 right-2 rounded-xl">
              <p className="bg-[#9D5596] text-white px-1 py-[2px] text-[10px] rounded-[2px]">
                广告
              </p>
            </div>
          </div>
        </div>
        {/* Video List */}
        <div className="grid grid-cols-2 gap-x-4 gap-y-3 px-5 py-3">
          {topicsList?.app_topics.map((appTopic) => (
            <div
              key={appTopic.id}
              className="w-full cursor-pointer"
              onClick={() => navigate(`/theme/detail/${appTopic.topic_id}`)}
            >
              <div className="relative">
                <ImageConcat
                  className="w-full aspect-video"
                  srcValue={appTopic.topic.cover}
                  alt={appTopic.topic.title}
                />
                <p className="text-white bg-[#262630CC] px-2 rounded text-[10px] absolute bottom-1 right-1">
                  {fmtDateSince(appTopic.topic.created_at)}
                </p>
              </div>
              <p className="text-xs mt-1">{appTopic.topic.title}</p>
            </div>
          ))}
        </div>
      </div>
    </TabWrapper>
  );
};

export default Theme;
