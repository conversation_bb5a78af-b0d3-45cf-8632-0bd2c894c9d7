import { useEffect, useState } from "react";
import TabWrapper from "../../components/TabWrapper";
import { NavLink, useNavigate } from "react-router";
import { Toast } from "antd-mobile";
import { useUser } from "../../contexts/UserContext";
import { UserInfoResponse } from "@/types/api";
import { useAppVersions } from "@/hooks/useApp";
// Import the new icon components
import CustomerServiceIcon from "@/components/icons/CustomerServiceIcon";
import HelpIcon from "@/components/icons/HelpIcon";
import RocketIcon from "@/components/icons/RocketIcon";
import LogoutIcon from "@/components/icons/LogoutIcon";
import PurchaseThemeIcon from "@/components/icons/PurchaseThemeIcon"; // Assuming this is the correct name/path

// Define type for menu items
interface MenuItem {
  id: number;
  name: string;
  desc?: string;
  iconComponent: React.ReactNode; // Or a more specific type like React.FC<{ size?: number; className?: string; color?: string }>
  requireLogin: boolean;
  path: string;
}

const initialMenuList2: MenuItem[] = [
  // {
  //   id: 1,
  //   name: "消息中心",
  //   desc: "查看系统消息",
  //   img: "/assets/images/message.png",
  //   requireLogin: true,
  //   path: "#",
  // },
  // {
  //   id: 2,
  //   name: "兑换页面",
  //   desc: "兑换VIP、主题",
  //   img: "/assets/images/exchange.png",
  //   requireLogin: true,
  //   path: "#",
  // },
  // {
  //   id: 3,
  //   name: "代理推广",
  //   desc: "VIP福利推广",
  //   img: "/assets/images/agent.png",
  //   requireLogin: false,
  //   path: "/me/agent-promote",
  // },
  {
    id: 4,
    name: "已购买主题",
    desc: "优质资源下载",
    iconComponent: <PurchaseThemeIcon className="w-5 h-5" />,
    requireLogin: false,
    path: "/me/purchased-theme",
  },
  // {
  //   id: 5,
  //   name: "福利开车群",
  //   desc: "一起开黄腔",
  //   img: "/assets/images/18.png",
  //   requireLogin: false,
  //   path: "#",
  // },
  {
    id: 6,
    name: "在线客服",
    desc: "24小时咨询",
    iconComponent: <CustomerServiceIcon className="w-5 h-5" />,
    requireLogin: false,
    path: "#",
  },
  {
    id: 7,
    name: "帮助与反馈",
    iconComponent: <HelpIcon className="w-5 h-5" />,
    requireLogin: false,
    path: "#",
  },
  {
    id: 8,
    name: "升级版本",
    desc: "版本1.0",
    iconComponent: <RocketIcon className="w-5 h-5" />,
    requireLogin: false,
    path: "#",
  },
  {
    id: 9,
    name: "退出登陆",
    iconComponent: <LogoutIcon className="w-5 h-5" />,
    requireLogin: true,
    path: "#",
  },
];

const Me = () => {
  const navigate = useNavigate();
  const [menuList2, setMenuList2] = useState<MenuItem[]>(initialMenuList2);
  const { user, isAuthenticated, logout } = useUser();
  const { data: appVersionData, isLoading: isVersionLoading } =
    useAppVersions();

  useEffect(() => {
    const APP_VERSION = process.env.APP_VERSION;
    const updatedList = initialMenuList2.map((item) => {
      if (item.id === 8) {
        let versionDesc = "检查更新";
        if (isVersionLoading) {
          versionDesc = "检查中...";
        } else if (appVersionData?.appVersions?.version_name) {
          versionDesc = `版本 ${APP_VERSION}`;
        }
        return { ...item, desc: versionDesc };
      }
      return item;
    });

    if (isAuthenticated) {
      setMenuList2(updatedList);
    } else {
      const filteredList = updatedList.filter(
        (item) => item?.requireLogin === false
      );
      setMenuList2(filteredList);
    }
  }, [isAuthenticated, appVersionData, isVersionLoading]);

  const handleLogout = () => {
    logout();

    Toast.show({
      icon: "success",
      content: "已成功退出登录",
    });

    navigate("/login");
  };

  return (
    <TabWrapper>
      <div className="overflow-y-scroll bg-[#F2F2F2] ">
        {/* Header */}
        <div className="meHeaderBg lg:h-[50vh] px-6">
          {/* Profile - Avatar */}
          <div className="pt-8 px-3 mb-4 lg:mb-6 flex items-center gap-3">
            <div className="w-[60px]">
              <img src="/assets/images/profile-xg.png" alt="avatar" />
            </div>
            {isAuthenticated ? (
              <div className="text-white">
                <p className="text-lg">{user?.user?.username}</p>
                <p className="text-sm">
                  {/* {user?.user?.status === 2
                    ? "永久VIP会员"
                    : user?.user?.status === 1
                    ? "VIP会员"
                    : "普通会员"} */}
                  {user?.user?.product_name}
                </p>
                <p className="text-sm">猫咪ID: {user?.user?.public_id}</p>
              </div>
            ) : (
              <p className="text-white text-base">请点击登录您的账号</p>
            )}
          </div>
          {/* Profile - Member VIP Info */}
          <div
            className={`relative ${
              user?.user?.status === 2 || user?.user?.status === 1
                ? "bg-textColorPrimary"
                : "bg-[#747c74]"
            } rounded-xl border-white border-2 mb-4`}
            onClick={() => navigate("/me/member-center")}
          >
            <div className="w-full">
              <img
                className="w-full"
                src={
                  user?.user?.product_id
                    ? "/assets/images/me-vip-bg-crown.png"
                    : "/assets/images/me-member-bg-crown.png"
                }
              />
            </div>
            <div className="absolute top-1/2 left-3 -translate-y-1/2">
              <p className="text-[18px] text-white font-medium">
                {user?.user.product_name || "会员中心"}
              </p>
              <p
                className={`text-sm ${
                  user?.user?.product_id
                    ? "text-textColorPrimary"
                    : "text-white"
                }`}
              >
                {user?.user?.product_id ? (
                  <span className="text-textColorPrimaryDark font-medium">
                    {user?.user?.expired_at
                      ? `将于 ${new Date(
                          Number(user.user.expired_at) * 1000
                        ).toLocaleDateString()} 到期`
                      : "快来开通会员吧"}
                  </span>
                ) : (
                  "快来开通会员吧"
                )}
              </p>
            </div>
            <div className="absolute top-0 right-0">
              {/* <p className="px-5 py-[2px] text-white rounded-full bg-gradient-to-l from-[#FD9657] from-0%  to-[#FF0000] to-100%">
                限时优惠
              </p> */}
            </div>
          </div>
          <div className="bg-white p-4 mb-5">
            {/* 设置 */}
            <div className="border-b border-disabledGrey/50 py-3">
              <NavLink
                to="/me/setting"
                className="text-[#333] flex items-center justify-between "
              >
                <div className="flex items-center gap-2 pl-2">
                  <img
                    src="/assets/images/setting.png"
                    alt="setting"
                    width={18}
                  />
                  <p>设置</p>
                </div>
                <div className="pr-2">
                  <p className="text-[11px] text-disabledIconGrey">手机绑定</p>
                </div>
              </NavLink>
            </div>
            {/* 购买流水 */}
            <div className="py-3">
              <NavLink
                to="/me/purchase-flow"
                className="text-[#333] flex items-center justify-between "
              >
                <div className="flex items-center gap-2 pl-2">
                  <img
                    src="/assets/images/transaction.png"
                    alt="setting"
                    width={18}
                  />
                  <p>购买流水</p>
                </div>
                <div className="pr-2">
                  <p className="text-[11px] text-disabledIconGrey">
                    消费流水记录
                  </p>
                </div>
              </NavLink>
            </div>
          </div>
        </div>
        <div className="relative bg-[#F2F2F2] h-[50vh]">
          <div className="pb-[75px] absolute -top-2 left-0 w-full px-6">
            <div className="bg-white p-4">
              {menuList2?.map((item, index) => {
                if (index + 1 === menuList2?.length) {
                  return (
                    <div
                      className={`py-3 ${
                        index + 1 === menuList2?.length
                          ? "border-b-0"
                          : "border-b border-b-disabledIconGrey/30"
                      }`}
                      key={item?.id}
                    >
                      {item?.id === 9 ? (
                        <div
                          onClick={handleLogout}
                          className="flex items-center justify-between cursor-pointer"
                        >
                          <div className="flex items-center gap-2 pl-2">
                            {item?.iconComponent}
                            <p className="text-[#333]">{item?.name}</p>
                          </div>
                          <div className="pr-2">
                            <p className="text-[11px] text-disabledIconGrey">
                              {item?.desc}
                            </p>
                          </div>
                        </div>
                      ) : (
                        <NavLink
                          to={item?.path}
                          className="flex items-center justify-between"
                        >
                          <div className="flex items-center gap-2 pl-2">
                            {item?.iconComponent}
                            <p className="text-[#333]">{item?.name}</p>
                          </div>
                          <div className="pr-2">
                            <p className="text-[11px] text-disabledIconGrey">
                              {item?.desc}
                            </p>
                          </div>
                        </NavLink>
                      )}
                    </div>
                  );
                } else {
                  return (
                    <div
                      className="border-b border-b-disabledIconGrey/30 py-3"
                      key={item?.id}
                    >
                      <NavLink
                        to={item?.path}
                        className="flex items-center justify-between"
                      >
                        <div className="flex items-center gap-2 pl-2">
                          {item?.iconComponent}
                          <p className="text-[#333]">{item?.name}</p>
                        </div>
                        <div className="pr-2">
                          <p className="text-[11px] text-disabledIconGrey">
                            {item?.desc}
                          </p>
                        </div>
                      </NavLink>
                    </div>
                  );
                }
              })}
            </div>
          </div>
        </div>
      </div>
    </TabWrapper>
  );
};

export default Me;
