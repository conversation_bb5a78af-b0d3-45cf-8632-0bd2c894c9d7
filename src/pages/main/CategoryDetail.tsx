import { useState, useEffect, useMemo } from "react";
import { useParams, useNavigate, useLocation } from "react-router";
import { <PERSON><PERSON>, Popup, Toast } from "antd-mobile";
import { QRCodeCanvas } from "qrcode.react";
import {
  EyeInvisibleOutline,
  EyeOutline,
  SystemQRcodeOutline,
} from "antd-mobile-icons";
import Navbar from "../../components/Navbar";
import { useVideoDetails, useVideosRelated } from "@/hooks/useContent";
import VideoPlayer from "../../components/VideoPlayer";
import VideoCard from "@/components/common/VideoCard";
import { PageLoader } from "../../components/common";
import dayjs from "dayjs";
import Loader from "@/components/common/Loader";
import LoopIcon from "@/components/icons/loop-icon";
import { useUser } from "@/contexts/UserContext";

// VideoCard Skeleton component
const VideoCardSkeleton = () => (
  <div className="animate-pulse">
    <div className="relative w-full aspect-[365/250] bg-gray-200 rounded-lg"></div>
    <div className="mt-2 h-4 bg-gray-200 rounded w-3/4"></div>
  </div>
);

const CategoryDetail = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { id, category_name } = useParams();
  const {
    data,
    isLoading: isVideoLoading,
    isError,
  } = useVideoDetails(Number(id));
  const { isAuthenticated, isLoading: isAuthLoading, user } = useUser();
  console.log(data);
  // State to track the current batch index
  const [currentBatchIndex, setCurrentBatchIndex] = useState(0);
  const [sharePopupVisible, setSharePopupVisible] = useState(false);
  const [showQrCode, setShowQrCode] = useState(false);

  // Use optional chaining for shareLink initially, as data might not be ready
  const shareLink = data?.video?.share_link || window.location.href;

  // Call the hook to get related videos
  const { data: relatedVideos, isLoading: isRelatedVideosLoading } =
    useVideosRelated(Number(category_name));

  // Function to change to another batch of videos
  const changeBatch = () => {
    // Check if we have videos and multiple batches
    if (
      relatedVideos?.videos &&
      Array.isArray(relatedVideos.videos) &&
      relatedVideos.videos.length > 1
    ) {
      // Calculate the next batch index (cycle through available batches)
      const nextBatchIndex =
        (currentBatchIndex + 1) % relatedVideos.videos.length;
      setCurrentBatchIndex(nextBatchIndex);
    }
  };

  // Function to copy the link
  const copyLink = () => {
    Toast.show({ content: "链接已复制", position: "bottom" });
    navigator.clipboard.writeText(shareLink);
    // Optionally show a toast message
    // Toast.show({ content: '链接已复制', position: 'bottom' });
  };

  // Format date for display
  const formatDate = (dateString: string | number) => {
    // If dateString is a number (timestamp), use dayjs.unix(), otherwise use dayjs()
    return typeof dateString === "number"
      ? dayjs.unix(dateString).format("YYYY-MM-DD")
      : dayjs(dateString).format("YYYY-MM-DD");
  };
  console.log("isAuthLoading", isAuthLoading);
  useEffect(() => {
    // Ensure data and auth status are loaded before checking for redirect
    if (!isVideoLoading && isAuthLoading === false && data?.video) {
      if (data.video.is_paid === 1) {
        // If video is paid and user is not authenticated, redirect to login
        if (!isAuthenticated) {
          navigate("/login", { state: { from: location }, replace: true });
        }
        // If user is authenticated but not VIP (no product_id), redirect to member center
        else if (
          isAuthenticated &&
          (!user?.user?.product_id || user.user.product_id === null)
        ) {
          navigate("/me/member-center", { replace: true });
        }
      }
    }
  }, [
    isVideoLoading,
    isAuthLoading,
    data,
    isAuthenticated,
    user,
    navigate,
    location,
  ]);

  // 1. Handle Loading States
  if (isVideoLoading || isAuthLoading) {
    return (
      <div className="h-dvh">
        <Navbar title="视频详情" />
        <Loader />
      </div>
    );
  }

  // 2. Handle Video Fetch Error
  if (isError) {
    return (
      <div className="h-dvh">
        <Navbar title="视频详情" />
        {/* Consider a more specific error message */}
        <div className="pt-20 text-center">加载视频详情失败</div>
      </div>
    );
  }

  // 3. Handle Missing Data after loading and no error
  if (!data || !data.video) {
    return (
      <div className="h-dvh">
        <Navbar title="视频详情" />
        <div className="pt-20 text-center">未找到视频信息</div>
      </div>
    );
  }

  // 4. Handle pending redirect: If video is paid and user isn't logged in or isn't VIP, show loader while useEffect redirects.
  if (
    data.video.is_paid === 1 &&
    (!isAuthenticated ||
      (isAuthenticated &&
        (!user?.user?.product_id || user.user.product_id === null)))
  ) {
    return (
      <div className="h-dvh">
        <Navbar title="视频详情" />
        <Loader />
      </div>
    );
  }

  // 5. Destructure video (now safe)
  const { video } = data;

  // Update shareLink now that data is confirmed
  const confirmedShareLink = video.share_link || window.location.href;

  // Get the current batch of videos to display
  const currentBatchVideos =
    relatedVideos?.videos &&
    Array.isArray(relatedVideos.videos) &&
    relatedVideos.videos.length > 0
      ? relatedVideos.videos[currentBatchIndex]
      : [];

  const shouldLimit30Seconds = video.is_paid === 1 && !user?.user?.product_id;

  return (
    <div className="h-dvh">
      <Navbar title={video.title || "视频详情"} isVip={video.is_paid === 1} />
      <div className="pt-12">
        <VideoPlayer
          videoUrl={video.video_url}
          thumbUrl={video.thumb_url}
          isPaid={shouldLimit30Seconds}
        />
        <div className="px-3 pt-3 pb-7 bg-white border-b">
          {/* Title */}
          <p className="text-base text-black font-medium">{video.title}</p>
          {/* Description */}
          <div className="flex items-center justify-between text-disabledIconGrey py-1">
            <p className="text-xs">
              视频ID: {video.video_uid}丨{formatDate(video.created_at)}
            </p>
            <img
              width={15}
              src="/assets/images/share-grey.png"
              className="cursor-pointer"
              onClick={() => setSharePopupVisible(true)}
            />
          </div>
        </div>
      </div>
      <div>
        {/* 广告 */}
        <div className="p-3">
          <div className="relative">
            <img
              className="w-full h-full object-cover"
              src="/assets/images/as.png"
              alt="banner"
            />
            <div className="absolute w-full bottom-0 bannerDesc rounded-xl">
              <div className="pb-1 px-5 flex items-center justify-between ">
                <p className="text-white">猫咪APP新版出炉</p>
                <p className="bg-[#9D5596] text-white px-1 py-[2px] text-[10px] rounded-[2px]">
                  广告
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="p-3">
        <div className="flex items-center justify-between mb-2">
          <p className="font-medium">相关影片</p>
          <div
            className="flex items-center gap-1 cursor-pointer"
            onClick={changeBatch}
          >
            <p className="text-textColorPrimary">换一批</p>
            {/* <img width={15} src="/assets/images/loop.png" alt="loop" /> */}
            <LoopIcon size={15} />
          </div>
        </div>
        <div className="grid grid-cols-2 gap-4">
          {isRelatedVideosLoading ? (
            // Show 10 skeleton cards while loading
            Array(10)
              .fill(0)
              .map((_, index) => <VideoCardSkeleton key={index} />)
          ) : (
            <>
              {currentBatchVideos &&
                currentBatchVideos.map((relatedVideo) => (
                  <VideoCard
                    key={relatedVideo.id}
                    video={relatedVideo}
                    onClick={(video) =>
                      navigate(
                        `/category-detail/${video.category_id}/${video.id}`
                      )
                    }
                  />
                ))}
              {(!currentBatchVideos || currentBatchVideos.length === 0) && (
                <div className="col-span-2 text-center py-4 text-disabledIconGrey">
                  暂无相关影片
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Share Popup */}
      <Popup
        visible={sharePopupVisible}
        onMaskClick={() => {
          setSharePopupVisible(false);
          setShowQrCode(false); // Reset QR code state when closing popup
        }}
        bodyStyle={{
          borderTopLeftRadius: "10px",
          borderTopRightRadius: "10px",
          minHeight: "20vh",
        }}
        className="bottomPopup lg:max-w-[30vw]"
      >
        <div className="p-5">
          {/* Input and copy button */}
          {!showQrCode && (
            <div className="flex items-center border rounded-full overflow-hidden mb-5">
              <div className="pl-4 pr-2 text-textColorPrimary text-sm whitespace-nowrap">
                分享好友 |
              </div>
              <input
                type="text"
                readOnly
                value={confirmedShareLink}
                className="flex-grow px-2 py-2 text-sm outline-none bg-transparent text-textColorPrimary"
              />
              <button
                onClick={copyLink}
                className="gradient-primary text-white px-4 py-2 text-sm whitespace-nowrap"
              >
                复制链接
              </button>
            </div>
          )}

          {/* QR Code Display */}
          {showQrCode && (
            <div className="text-center mb-5 flex flex-col items-center justify-center">
              <QRCodeCanvas value={confirmedShareLink} size={128} />
              <p className="text-sm text-textColorPrimary mt-2">
                长按或右键保存二维码
              </p>
              <Button
                onClick={() => setShowQrCode(false)}
                className="mt-3 text-sm text-blue-500"
              >
                关闭
              </Button>
            </div>
          )}

          {/* Icons - Hide when QR code is shown */}
          {!showQrCode && (
            <div className="flex justify-around">
              <div className="text-center" onClick={() => setShowQrCode(true)}>
                <div className="w-14 h-14 flex items-center justify-center gradient-primary rounded-full mb-2 cursor-pointer">
                  <SystemQRcodeOutline fontSize={24} className="text-white" />
                </div>
                <p className="text-sm text-textColorPrimary">保存二维码</p>
              </div>
              {/* <div className="text-center">
                <div className="w-14 h-14 flex items-center justify-center bg-[#ffccd5] rounded-full mb-2 cursor-pointer">
                  <img src="/assets/images/Ellipse 91.png" alt="桃子APP" />
                </div>
                <p className="text-sm text-textColorPrimary">桃子APP</p>
              </div>
              <div className="text-center">
                <div className="w-14 h-14 flex items-center justify-center gradient-primary rounded-full mb-2 cursor-pointer">
                  <EyeInvisibleOutline fontSize={24} className="text-white" />
                </div>
                <p className="text-sm text-textColorPrimary">无法观看</p>
              </div> */}
            </div>
          )}
        </div>
      </Popup>
    </div>
  );
};

export default CategoryDetail;
