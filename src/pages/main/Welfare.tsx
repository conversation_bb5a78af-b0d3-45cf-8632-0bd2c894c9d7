import { useRef, useState } from "react";
import Tab<PERSON>rapper from "../../components/TabWrapper";
import CategoryPopup from "./modules/CategoryPopup";
// import { NavLink } from "react-router";
import { Swiper, SwiperRef, Tabs } from "antd-mobile";
// import Featured from "./modules/Featured";
import { useGlobalMenus } from "@/contexts/MenusContext";
import { BenefitItem } from "@/types/api";
import { ThemeColor } from "@/components/ThemeColor";
import MenuButton from "@/components/common/MenuButton";
// import Forum from "./modules/Forum";

const BenefitContent = ({ items }: { items: BenefitItem[] }) => {
  return (
    <div className="grid grid-cols-4 gap-4 p-4">
      {items.map((item) => (
        <a key={item.id} href={item.url} className="flex flex-col items-center">
          <div className="w-full aspect-square rounded-lg overflow-hidden mb-2">
            <img
              src={`${item.img}`}
              alt={item.title}
              className="w-full h-full object-cover"
            />
          </div>
          <span className="text-sm text-center text-ellipsis text-black overflow-hidden">
            {item.title}
          </span>
        </a>
      ))}
    </div>
  );
};

const Welfare = () => {
  const swiperRef = useRef<SwiperRef>(null);
  const [activeIndex, setActiveIndex] = useState(0);

  const { data } = useGlobalMenus();
  const benefits = data?.appMenus.benefits || [];
  console.log("benefits", benefits);
  // Convert benefits to tab items
  const tabItems = benefits.map((benefit) => ({
    key: benefit.id.toString(),
    title: benefit.title,
    children: <BenefitContent items={benefit.child || []} />,
  }));

  return (
    <TabWrapper>
      <ThemeColor color="primary" />
      {/* Header Menu */}
      <div className="fixed top-0 left-0 right-0 h-12 gradient-primary z-10 lg:max-w-[30vw] mx-auto">
        <Tabs
          activeKey={tabItems[activeIndex]?.key}
          onChange={(key) => {
            const index = tabItems.findIndex((item) => item.key === key);
            setActiveIndex(index);
            swiperRef.current?.swipeTo(index);
          }}
          className="mainMenuTabs"
        >
          {tabItems.map((item) => (
            <Tabs.Tab title={item.title} key={item.key} />
          ))}
        </Tabs>
        <MenuButton className="top-[40%]" />
      </div>
      {/* Body */}
      <Swiper
        direction="horizontal"
        loop
        indicator={() => null}
        ref={swiperRef}
        defaultIndex={activeIndex}
        onIndexChange={(index) => {
          setActiveIndex(index);
        }}
        className="flex-1 pt-12"
      >
        {tabItems.map((item) => (
          <Swiper.Item key={item.key}>{item.children}</Swiper.Item>
        ))}
      </Swiper>
    </TabWrapper>
  );
};

export default Welfare;
