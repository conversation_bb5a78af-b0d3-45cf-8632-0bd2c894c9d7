import { useEffect, useState, useRef, useCallback } from "react";
import Navbar from "../../components/Navbar";
import { useNavigate, useParams } from "react-router";
import { useVideosList } from "@/hooks/useContent";
import { useGlobalMenus } from "@/contexts/MenusContext";
import VideoCard from "@/components/common/VideoCard";
import { Video } from "@/types/api";
import { DotLoading } from "antd-mobile";
import { Waypoint } from "react-waypoint";
import MenuButton from "@/components/common/MenuButton";
import NoRecord from "@/components/common/NoRecord";

// Simplified category structure - no ids, no nesting
const typeCategories = [
  {
    name: "最新视频",
    value: "latest",
  },
  {
    name: "热门视频",
    value: "popular",
  },
];

const durationCategories = [
  {
    name: "全部视频",
    value: "all",
  },
  {
    name: "长视频",
    value: "long",
  },
  {
    name: "短视频",
    value: "short",
  },
];

const CategoryList = () => {
  const navigate = useNavigate();
  const [selectedType, setSelectedType] = useState<string>("latest");
  const [selectedDurationType, setSelectedDurationType] =
    useState<string>("all");
  const [page, setPage] = useState(1);
  const [allVideos, setAllVideos] = useState<Video[]>([]);
  const [hasMore, setHasMore] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [contentHeight, setContentHeight] = useState<string>("auto");

  const filtersRef = useRef<HTMLDivElement>(null);

  const { name } = useParams();
  const { data: menus } = useGlobalMenus();

  const menuId = Number(name) || undefined;

  const flattenedNormalMenus = (menus?.appMenus?.normal || [])
    .filter(Boolean) // Filter out potential null/undefined top-level items
    .flatMap((item: any) => {
      // Assuming 'item' can have a 'child' array which also contains menu items
      const children =
        item.child && Array.isArray(item.child)
          ? item.child.filter(Boolean) // Filter out potential null/undefined children
          : [];
      return [item, ...children]; // Return the item and its direct children
    });

  const selectedMenu = flattenedNormalMenus.find(
    (item: any) => item?.id === Number(name)
  );

  // Handle category type changes
  const handleTypeChange = useCallback((value: string) => {
    setSelectedType(value);
    setPage(1);
    setAllVideos([]);
    setHasMore(true);
  }, []);

  // Handle duration type changes
  const handleDurationChange = useCallback((value: string) => {
    setSelectedDurationType(value);
    setPage(1);
    setAllVideos([]);
    setHasMore(true);
  }, []);

  const {
    data: videosData,
    isLoading,
    isFetching,
  } = useVideosList({
    menu_id: menuId,
    page,
    limit: 10,
    type: selectedType,
    duration_type: selectedDurationType,
  });

  // Update videos when data changes
  useEffect(() => {
    if (videosData?.videos) {
      if (page === 1) {
        // Replace videos on first page
        setAllVideos(videosData.videos.data || []);
      } else {
        // Append videos for subsequent pages
        setAllVideos((prev) => [...prev, ...(videosData.videos.data || [])]);
      }

      // Check if there are more pages
      setHasMore(videosData.videos.current_page < videosData.videos.last_page);
      setIsLoadingMore(false);
    }
  }, [videosData, page]);

  // Reset pagination when menuId changes
  useEffect(() => {
    setPage(1);
    setAllVideos([]);
    setHasMore(true);
    setIsLoadingMore(false);
  }, [menuId]);

  // Calculate content height
  useEffect(() => {
    const updateContentHeight = () => {
      if (filtersRef.current) {
        // Get navbar height (fixed at 48px) + filters section height

        const filtersHeight = filtersRef.current.offsetHeight;

        // Set the content height to viewport height minus the header sections
        setContentHeight(`calc(100% - ${filtersHeight}px)`);
      }
    };

    // Initial calculation
    updateContentHeight();

    // Recalculate on window resize
    window.addEventListener("resize", updateContentHeight);

    return () => {
      window.removeEventListener("resize", updateContentHeight);
    };
  }, []);

  const handleVideoClick = (video: Video) => {
    navigate(`/category-detail/${video.category_id}/${video.id}`);
  };

  const loadMoreVideos = () => {
    if (!isLoading && hasMore && !isLoadingMore) {
      setIsLoadingMore(true);
      setPage((prev) => prev + 1);
    }
  };

  // Determine if we're in a loading state for the current page
  const isPageLoading = (isLoading || isFetching) && page === 1;

  return (
    <>
      <div className="h-dvh">
        <Navbar
          title={selectedMenu?.title || "类别列表"}
          addOnAfter={<MenuButton className="top-[40%]" />}
        />
        {/* 类型 */}
        <div className="pt-12" ref={filtersRef}>
          <div className="flex items-center gap-4 py-2 px-4 border-b border-disabledIconGrey/30">
            <p className="text-base text-textColorPrimary">类型</p>
            <div className="flex items-center gap-3">
              {typeCategories.map((item, index) => (
                <p
                  key={index}
                  onClick={() => handleTypeChange(item.value)}
                  className={`${
                    selectedType === item.value ? "text-textColorPrimary" : ""
                  }`}
                >
                  {item.name}
                </p>
              ))}
            </div>
          </div>
          <div className="flex items-center gap-4 py-2 px-4 border-b border-disabledIconGrey/30">
            <p className="text-base text-textColorPrimary">类型</p>
            <div className="flex items-center gap-3">
              {durationCategories.map((item, index) => (
                <p
                  key={index}
                  onClick={() => handleDurationChange(item.value)}
                  className={`${
                    selectedDurationType === item.value
                      ? "bg-textColorPrimary text-white px-3 py-1 rounded"
                      : "bg-disabledGrey text-white px-3 py-1 rounded"
                  }`}
                >
                  {item.name}
                </p>
              ))}
            </div>
          </div>
        </div>
        {/* 视频 */}
        <div className="p-3 overflow-y-auto" style={{ height: contentHeight }}>
          {isPageLoading ? (
            <div className="flex justify-center items-center h-40">
              <DotLoading color="primary" />
            </div>
          ) : allVideos.length > 0 ? (
            <div className="grid grid-cols-2 gap-4">
              {allVideos.map((video: Video) => (
                <VideoCard
                  key={video.id}
                  video={video}
                  onClick={handleVideoClick}
                />
              ))}

              {/* Waypoint for lazy loading */}
              {hasMore && (
                <Waypoint
                  onEnter={loadMoreVideos}
                  bottomOffset="-20%"
                  topOffset="20%"
                >
                  <div className="col-span-2 h-10 flex justify-center items-center">
                    {isLoadingMore && <DotLoading color="primary" />}
                  </div>
                </Waypoint>
              )}
            </div>
          ) : (
            <NoRecord />
          )}
        </div>
      </div>
    </>
  );
};

export default CategoryList;
