import { DotLoading } from "antd-mobile";
import { useLocation, useNavigate } from "react-router";
import { Video } from "@/types/api";
import VideoCard from "@/components/common/VideoCard";

interface SubcategoryContentProps {
  id: number;
  title: string;
  categoryId: number | null;
  videos: Video[];
  isLoading: boolean;
}

const SubcategoryContent = ({
  id,
  title,
  categoryId,
  videos,
  isLoading,
}: SubcategoryContentProps) => {
  const navigate = useNavigate();

  const handleVideoClick = (video: Video) => {
    navigate(`/category-detail/${video.category_id}/${video.id}`);
  };
  // get pathname
  const pathname = useLocation().pathname;
  const isVip = pathname.includes("vip");
  return (
    <div>
      <div className="flex items-center justify-between px-2 mb-1">
        <p className="text-base font-semibold">{title}</p>
        <p
          className="text-textColorPrimary text-sm"
          onClick={() => navigate(`/category-list/${id || ""}`)}
        >
          更多
        </p>
      </div>
      {isLoading ? (
        <div className="flex justify-center items-center h-40">
          <DotLoading color="primary" />
        </div>
      ) : (
        <>
          {videos.length > 0 ? (
            <div>
              {/* Featured video */}
              <div className="mb-4">
                <VideoCard
                  video={videos[0]}
                  onClick={handleVideoClick}
                  aspectRatioClass="aspect-[750/350]"
                />
              </div>

              {/* Grid of videos */}
              <div className="grid grid-cols-2 gap-4">
                {videos.slice(1, 5).map((video) => (
                  <VideoCard
                    key={video.id}
                    video={video}
                    onClick={handleVideoClick}
                  />
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              No videos available
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default SubcategoryContent;
