import { useState } from "react";
import { menuListType } from "../../../utils/types/home";
import MarqueeNoticeBar from "../../../components/MarqueeNoticeBar";
import { Tabs } from "antd-mobile";

const submenuList = [
  {
    id: 1,
    name: "每日发现",
    value: "dailyNews",
  },
  {
    id: 2,
    name: "视频",
    value: "video",
  },
  {
    id: 3,
    name: "投稿",
    value: "contribute",
  },
  {
    id: 4,
    name: "自拍",
    value: "selfie",
  },
  {
    id: 5,
    name: "番号",
    value: "number",
  },
];

const Forum = () => {
  const [submenuSelected, setSubmenuSelected] = useState("dailyNews");

  return (
    <div>
      {/* Submenu */}
      <div className="bg-white px-4 py-3 flex gap-3 items-center">
        {submenuList?.map((item: menuListType) => {
          return (
            <p
              key={item?.id}
              className={`${
                submenuSelected === item?.value
                  ? "text-textColorPrimary text-sm font-medium"
                  : "text-sm"
              } `}
              onClick={() => setSubmenuSelected(item?.value)}
            >
              {item?.name}
            </p>
          );
        })}
      </div>
      {/* Forum - Header */}
      <div>
        <div className="py-3 px-6 flex items-center justify-between">
          {/* Profile 和 发帖说明*/}
          <div>
            {/* Profile */}
            <div className="flex items-center gap-3">
              <div className="w-[55px]">
                <img src="/assets/images/profile.png" alt="avatar" />
              </div>
              <div>
                <p className="text-sm">TEXT123</p>
                <div className="flex items-center gap-2 mt-1 mb-2">
                  <div className="flex items-center gap-1">
                    <img
                      className="w-4 h-4"
                      src="/assets/images/coin.png"
                      alt="coin"
                    />
                    <p className="text-xs text-textColorOrange">20K</p>
                  </div>
                  <div className="flex items-center gap-1">
                    <img
                      className="w-4 h-4"
                      src="/assets/images/diamond.png"
                      alt="diamond"
                    />
                    <p className="text-xs text-textColorBlue">100</p>
                  </div>
                  <img
                    className="w-3 h-3"
                    src="/assets/images/arrow-right-grey.png"
                    alt="right"
                  />
                </div>
              </div>
            </div>
            {/* 发帖说明 */}
            <div className="text-disabledIconGrey text-xs my-2">
              <p>发帖说明</p>
              <ul>
                <li>
                  <p>1.出售贴取整数</p>
                </li>
                <li>
                  <p>2.普通用户发帖获得20金币，最高发帖10次。</p>
                </li>
                <li>
                  <p>3.金币贴与砖石贴限制VIP发布。</p>
                </li>
                <li>
                  <p>4.VIP用户发布砖石最高获得70%收益。</p>
                </li>
              </ul>
            </div>
          </div>
          {/* 发布普通贴 和 发布金币 */}
          <div className="flex items-center gap-4">
            <div>
              <img
                className="w-[65px] my-0 mx-auto"
                src="/assets/images/post-normal.png"
                alt="normal"
              />
              <p className="text-iconColorPrimary text-[11px] text-center">
                发布普通贴
              </p>
            </div>
            <div>
              <img
                className="w-[65px] my-0 mx-auto"
                src="/assets/images/post-gold.png"
                alt="gold"
              />
              <p className="text-textColorOrange text-[11px] text-center">
                发布金币/砖石贴
              </p>
            </div>
          </div>
        </div>
        {/* view -  今日 主题 回复 被点赞*/}
        <div className="flex items-center justify-between py-2 px-6 bg-white">
          <div className="text-center">
            <p className="text-base">18</p>
            <p className="text-xs text-disabledGrey">今日</p>
          </div>
          <div className="text-center">
            <p className="text-base">576</p>
            <p className="text-xs text-disabledGrey">主题</p>
          </div>
          <div className="text-center">
            <p className="text-base">6.2 </p>
            <p className="text-xs text-disabledGrey">回复</p>
          </div>
          <div className="text-center">
            <p className="text-base">182.2W</p>
            <p className="text-xs text-disabledGrey">被点赞</p>
          </div>
        </div>
        <div className="px-2">
          <MarqueeNoticeBar content="专属终身VIP会员特惠,看海量视频,看海量视频,享多重福利, 享多重福利,享多重福利" />
        </div>
        {/* 列表 */}
        <div className="bg-white">
          <Tabs defaultActiveKey="default" className="listTabs">
            <Tabs.Tab title="默认" key="default">
              <div className="border-b mt-2 mb-4 pb-10">
                {/* Header */}
                <div className="flex items-center justify-between">
                  {/* Profie */}
                  <div className="flex items-center gap-2">
                    <img
                      className="w-[40px]"
                      src="/assets/images/profile.png"
                      alt="profile"
                    />
                    <div>
                      <p>踏实的吐司</p>
                      <p>2024-05-12</p>
                    </div>
                  </div>
                  {/* 关注 */}
                  <div>
                    <button className="border border-textColorPrimary text-textColorPrimary px-6 py-1 rounded-full">
                      关注
                    </button>
                  </div>
                </div>
                {/* Desc */}
                <div>
                  <div className="my-2">
                    <p>你说我不懂你的黑色幽默，我笑你不解风情</p>
                  </div>
                  <div className="grid grid-cols-3 gap-y-2 gap-x-2">
                    <img
                      className="w-full"
                      src="/assets/images/parcel.png"
                      alt="parcel"
                    />
                    <img
                      className="w-full"
                      src="/assets/images/parcel.png"
                      alt="parcel"
                    />
                    <img
                      className="w-full"
                      src="/assets/images/parcel.png"
                      alt="parcel"
                    />
                    <img
                      className="w-full"
                      src="/assets/images/parcel.png"
                      alt="parcel"
                    />
                  </div>
                  <div className="flex items-center gap-4 justify-end">
                    <div className="flex items-center gap-[2px]">
                      <img
                        className="w-[16px] object-contain cursor-pointer"
                        src="/assets/images/like.png"
                        alt="like"
                      />
                      <p className="text-sm">38</p>
                    </div>
                    <div className="flex items-center gap-[2px]">
                      <img
                        className="w-[16px] object-contain rotate-180 cursor-pointer"
                        src="/assets/images/like.png"
                        alt="like"
                      />
                      <p className="text-sm">0</p>
                    </div>
                  </div>
                </div>
              </div>
            </Tabs.Tab>
            <Tabs.Tab title="最新" key="latest">
              最新
            </Tabs.Tab>
            <Tabs.Tab title="热门" key="hot">
              热门
            </Tabs.Tab>
            <Tabs.Tab title="精华" key="essence">
              精华
            </Tabs.Tab>
            <Tabs.Tab title="出售" key="sale">
              出售
            </Tabs.Tab>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default Forum;
