import { useState } from "react";
import Navbar from "../../../components/Navbar";
import { DotLoading, Modal, Tabs } from "antd-mobile";
import { NavLink, useNavigate, useParams } from "react-router";
import { useTopicDetails } from "@/hooks/useContent";
import Image from "@/components/common/Image";
import ImageConcat from "@/components/common/ImageConcat";
import dayjs from "dayjs";
import { Topic, Video } from "@/types/api";
import { fmtDateSince } from "@/utils/utils";
import MenuButton from "@/components/common/MenuButton";

const ThemeDetail = () => {
  const navigate = useNavigate();

  const isVip: number = 1;
  const [downloadBundleVisible, setDownloadBundleVisible] = useState(false);

  const { topic_id } = useParams();

  const { data: topicDetail, isLoading } = useTopicDetails(topic_id!);
  console.log(topicDetail);
  const handleBuyClick = () => {
    navigate(
      `/me/payment/${topic_id}?isTopic=true&price=${topicDetail?.app_topic?.price}`
    );
  };

  const handleVipClick = () => {
    navigate(`/me/member-center`);
  };

  // Video card component to avoid duplication
  const VideoCard = ({
    video,
    isFree = false,
    aspectRatio = "",
  }: {
    video: Video;
    isFree?: boolean;
    aspectRatio?: string;
  }) => (
    <div
      key={video.id}
      className="border rounded mb-4 h-full"
      onClick={() =>
        navigate(`/category-detail/${video.category_id}/${video.id}`)
      }
    >
      {/* Image */}
      <div className="relative">
        <ImageConcat
          srcValue={video.thumb_url}
          alt={video.title}
          className={`w-full`}
          aspectRatio={aspectRatio}
        />
        {isFree && (
          <div className="absolute top-2 right-2 bg-iconColorPrimary text-white px-2 py-1 rounded-md text-xs font-medium z-10">
            试看
          </div>
        )}
        <div className="absolute w-full bottom-0 rounded-xl">
          <div className="pb-2 px-3 flex items-center justify-between ">
            <p className="text-white bg-[#262630CC] px-2 rounded text-sm">
              0:07:59
            </p>
            <p className="text-white px-1 py-[2px] text-[10px] rounded-[2px] text-sm">
              {fmtDateSince(video.created_at)}
            </p>
          </div>
        </div>
      </div>
      {/* Content */}
      <div className="p-2">
        <p className="text-base font-medium text-black">{video.title}</p>
        <p className="text-xs">{video.title}</p>
        <NavLink to="#" className="text-iconColorPrimary text-right text-sm">
          <p>显示更多</p>
        </NavLink>
      </div>
    </div>
  );

  if (!topicDetail?.app_topic && isLoading) {
    return (
      <>
        <Navbar title="视频详情" />
        <div className="flex justify-center items-center h-[50vh] pt-12">
          <DotLoading color="primary" />
        </div>
      </>
    );
  }

  const topic: Topic = topicDetail?.app_topic?.topic || {
    id: 0,
    topic_uid: "",
    title: "",
    description: "",
    cover: "",
    gif_images: [],
    price: "0",
    discount: "0",
    status: 0,
    created_at: 0,
    updated_at: 0,
    deleted_at: null,
    paid_videos: [],
    free_videos: [],
  };

  return (
    <div className=" bg-white">
      <Navbar title={topic.title} addOnAfter={<MenuButton />} />
      {/* Header */}
      <div className="pt-12">
        <div className="w-full relative">
          <ImageConcat
            className="w-full"
            srcValue={topic.cover}
            alt={topic.title}
            aspectRatio="2/1"
          />{" "}
          <div className="absolute inset-0 bg-white/50 pointer-events-none"></div>
          {/* <div className="w-[140px] text-white absolute top-2 right-2">
            <button
              className={`w-full ${
                isVip === 0
                  ? "bg-disabledIconGrey cursor-not-allowed"
                  : "bg-iconColorPrimary cursor-pointer"
              } py-2 px-3 rounded-lg shadow-3xl`}
              onClick={handleDownloadBundle}
            >
              下载捆绑包
            </button>
          </div> */}
          <div className="px-5 -mt-[20%] relative z-10">
            <div className="bg-white px-4 py-3 rounded-lg border">
              <div className="flex items-start border-b border-b-disabledIconGrey/30 pb-3">
                <p className="flex-1 text-xs  text-iconColorPrimary">
                  内容介绍
                </p>
                <p
                  className="flex-5 text-xs text-disabledIconGrey"
                  dangerouslySetInnerHTML={{
                    __html: topic.description,
                  }}
                ></p>
              </div>
              <div>
                <div className="flex items-center justify-end gap-2 my-1">
                  <div className="text-right">
                    <p className="text-[10px] text-iconColorPrimary font-medium">
                      会员价: {topicDetail?.app_topic?.price}元
                    </p>
                    <p className="text-sm text-disabledIconGrey">
                      当前售价: {topic.price}元
                    </p>
                  </div>
                  <div className="flex items-center border border-textColorGold px-2 py-1 rounded-md">
                    <p className="text-textColorGold" onClick={handleVipClick}>
                      开通VIP
                    </p>
                    <img
                      className="w-[18px]"
                      src="/assets/images/crown-gold.png"
                      alt="crown"
                    />
                  </div>
                </div>
                <button
                  className="w-full bg-iconColorPrimary py-2 px-3 rounded-lg shadow-3xl text-white"
                  onClick={handleBuyClick}
                >
                  立即购买
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Body - 列表 */}
      <div>
        <Tabs defaultActiveKey="list" className="listTabs">
          <Tabs.Tab title="视频列表" key="list">
            <div className="grid grid-cols-2 gap-3 p-3">
              {topic.paid_videos?.map((video, index) => (
                <div key={video.id} className={index === 0 ? "col-span-2" : ""}>
                  <VideoCard
                    video={video}
                    aspectRatio={index === 0 ? "750/350" : "365/250"}
                  />
                </div>
              ))}
              {!topic.paid_videos?.length && (
                <div className="text-center py-4 text-disabledIconGrey col-span-2">
                  暂无付费视频
                </div>
              )}
            </div>
          </Tabs.Tab>
          <Tabs.Tab title="视频片段" key="video">
            <div className="grid grid-cols-2 gap-3 p-3">
              {topic.gif_images?.map((gifImage, index) => (
                <div key={index} className="border rounded overflow-hidden">
                  <ImageConcat
                    srcValue={gifImage}
                    alt={`GIF Preview ${index + 1}`}
                    className="w-full"
                  />
                </div>
              ))}
              {!topic.gif_images?.length && (
                <div className="text-center py-4 text-disabledIconGrey col-span-2">
                  暂无视频片段
                </div>
              )}
            </div>
          </Tabs.Tab>
          <Tabs.Tab title="免费视频" key="free">
            <div className="grid grid-cols-2 gap-3 p-3">
              {topic.free_videos?.map((video, index) => (
                <div key={video.id} className={index === 0 ? "col-span-2" : ""}>
                  <VideoCard video={video} isFree={true} />
                </div>
              ))}
              {!topic.free_videos?.length && (
                <div className="text-center py-4 text-disabledIconGrey col-span-2">
                  暂无免费视频
                </div>
              )}
            </div>
          </Tabs.Tab>
        </Tabs>
      </div>

      <Modal
        visible={downloadBundleVisible}
        closeOnAction
        showCloseButton={true}
        onClose={() => {
          setDownloadBundleVisible(false);
        }}
        className="downloadBundleModal"
        content={
          <div>
            <p className="text-center text-lg font-medium">下载链接</p>
            <div>
              <div className="flex items-start gap-2 my-3">
                <input
                  type="text"
                  value="thunder://QUFodhsuowniehushiohoih.com"
                  className="flex-3 py-2 rounded-lg px-3 border border-disabledIconGrey/50 text-xs"
                />
                <button className="bg-iconColorPrimary text-white px-4 py-[6px] rounded-lg flex items-center gap-2">
                  <img
                    className="w-[16px]"
                    src="/assets/images/xunlei.png"
                    alt="xunlei"
                  />
                  迅雷下载
                </button>
              </div>
              <div className="flex items-start gap-2 my-3">
                <input
                  type="text"
                  value="https://www.mmxzfyaiuhiusiho.com"
                  className="flex-3 py-2 rounded-lg px-3 border border-disabledIconGrey/50 text-xs"
                />
                <button className="bg-iconColorPrimary text-white px-4 py-[6px] rounded-lg flex items-center gap-2">
                  <img
                    className="w-[16px]"
                    src="/assets/images/fdm.png"
                    alt="fdm"
                  />
                  FDM下载
                </button>
              </div>
            </div>
          </div>
        }
      />
    </div>
  );
};

export default ThemeDetail;
