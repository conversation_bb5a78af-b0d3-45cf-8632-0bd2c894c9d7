import { useState } from "react";
import { menuListType } from "../../../utils/types/home";

const submenuList = [
  {
    id: 1,
    name: "猫咪头条",
    value: "topStories",
  },
  {
    id: 2,
    name: "直播视频",
    value: "liveVideo",
  },
];

const Featured = () => {
  const [submenuSelected, setSubmenuSelected] = useState("topStories");
  return (
    <div>
      {/* Submenu */}
      <div className="bg-white px-4 py-3 flex gap-3 items-center">
        {submenuList?.map((item: menuListType) => {
          return (
            <p
              key={item?.id}
              className={`${
                submenuSelected === item?.value
                  ? "text-textColorPrimary text-sm font-medium"
                  : "text-sm"
              } `}
              onClick={() => setSubmenuSelected(item?.value)}
            >
              {item?.name}
            </p>
          );
        })}
      </div>
      {/* Background Image */}
      <div>
        <img className="w-full" src="/assets/images/welfare-feature-bg.png" />
      </div>
      {/* Content */}
      <div className="px-5 -mt-[25%] relative z-10">
        {/* Header - profile &  进入官网 按钮 */}
        <div className=" flex items-center justify-between mb-3">
          <div className="flex items-center gap-4 flex-1">
            <img
              className="w-[55px]"
              src="/assets/images/top-stories-profile.png"
            />
            <p className="text-white text-lg">猫咪头条</p>
          </div>
          <div className="flex-1 text-right">
            <button className="bg-iconColorPrimary py-2 px-9 rounded-lg shadow-3xl text-white">
              进入官网
            </button>
          </div>
        </div>
        {/* Body - Description */}
        <div className="bg-white px-4 py-3 rounded-lg border">
          <p>
            了解最新性福资讯，百万资源尽手掌握！包含：猫咪，快猫，快狐，猫咪段子，狼友圈，sexmcc论坛，麻豆等多平台热门免费资源；更有原创博主，热门黑料，杂志，音频，小说，楼凤，电影等多类型资源展示！
          </p>
        </div>
      </div>
    </div>
  );
};

export default Featured;
