import Navbar from "../../../components/Navbar";
import Dplayer from "../../../components/Dplayer";
import { Tabs } from "antd-mobile";

const ThemeVideo = () => {
  return (
    <div className="h-dvh bg-white">
      <Navbar title="柚子猫合集" />
      <div className="pt-12">
        <Dplayer />
        {/* 视频详情 */}
        <div className="px-3 py-2">
          <p className="text-base font-medium mb-1">
            [六月精选AV] 六月精选AV-岛国女优-性爱大狂 欢-大乱交
          </p>
          <p className="text-sm text-disabledIconGrey mb-2">
            小樱本身是一位COS美女网红,
            由于貌美的脸蛋和纤瘦的身材囊获不少粉丝前来支持并帮她摄影~
            有一次她身穿性感露奶学生装在某学校课室给粉丝拍摄时，性感的装扮令在场的2位粉丝的大肉棒变得又大又硬~
            为了回馈他们对小樱的支持，她主动抚摸粉丝的大肉棒，并含舔和撸一撸大肉棒，并让粉丝插入她的小穴一阵猛操之下，最后粉丝将大量的白浆射满在她的嫩逼上，让她满足！！
          </p>
          <div className="flex items-center justify-between">
            <p className="text-sm text-disabledIconGrey mb-2 font-light">
              六月精选AV丨2024-06-09
            </p>
            <div className="flex items-center gap-5">
              <div className="flex items-center gap-4">
                <img
                  className="w-[16px]"
                  src="/assets/images/share-grey.png"
                  alt="share"
                />
                <img
                  className="w-[16px]"
                  src="/assets/images/comment.png"
                  alt="comment"
                />
              </div>
              <p className="text-iconColorPrimary text-sm">默认路线</p>
            </div>
          </div>
        </div>
        {/* Tabs */}
        <div>
          <Tabs defaultActiveKey="download" className="listTabs">
            <Tabs.Tab title="视频下载" key="download">
              <div>
                <div className="flex items-start gap-4 my-3">
                  <input
                    type="text"
                    value="thunder://QUFodhsuowniehushiohoih.com"
                    className="flex-2 py-2 rounded-lg px-3 border border-disabledIconGrey/50"
                  />
                  <button className="bg-[#3BA8D0] text-white px-5 py-2 rounded-lg flex items-center gap-2">
                    <img
                      className="w-[20px]"
                      src="/assets/images/xunlei.png"
                      alt="xunlei"
                    />
                    迅雷下载
                  </button>
                </div>
                <div className="flex items-start gap-4 my-3">
                  <input
                    type="text"
                    value="https://www.mmxzfyaiuhiusiho.com"
                    className="flex-2 py-2 rounded-lg px-3 border border-disabledIconGrey/50"
                  />
                  <button className="bg-[#3BA8D0] text-white px-5 py-2 rounded-lg flex items-center gap-2">
                    <img
                      className="w-[20px]"
                      src="/assets/images/fdm.png"
                      alt="fdm"
                    />{" "}
                    FDM下载
                  </button>
                </div>
              </div>
            </Tabs.Tab>
            <Tabs.Tab title="视频片段" key="clips">
              视频片段
            </Tabs.Tab>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default ThemeVideo;
