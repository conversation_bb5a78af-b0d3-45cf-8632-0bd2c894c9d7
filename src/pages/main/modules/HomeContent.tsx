import { Swiper } from "antd-mobile";
import MarqueeNoticeBar from "@components/MarqueeNoticeBar";
import { useRef, useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router";
import {
  MenuItem,
  MenuItemChild,
  Video,
  VideosListResponse,
} from "@/types/api";
import { useAppConfig, useBanners } from "@/hooks/useApp";
import SubcategoryContent from "./SubcategoryContent";
import { API_ENDPOINTS, APP_KEY } from "@/config/api-endpoints";
import { postRequest } from "@/utils/query";

interface HomeContentProps {
  menuItem: MenuItem;
  theme?: "primary" | "gold";
  activeTabId?: string;
  isFirstTab?: boolean;
}

interface SubmenuData {
  submenu: MenuItemChild;
  videos: Video[];
  isLoading: boolean;
}

const HomeContent = ({
  menuItem,
  theme = "primary",
  isFirstTab = false,
}: HomeContentProps) => {
  const { data: config } = useAppConfig();
  const { data: bannersData, isLoading: isBannersLoading } = useBanners();

  const [activeSubmenuId, setActiveSubmenuId] = useState<number | null>(
    menuItem?.child?.length > 0 ? menuItem.child[0].id : null
  );
  const containerRef = useRef<HTMLDivElement>(null);

  // Create refs for each submenu section
  const sectionRefs = useRef<{ [key: number]: HTMLDivElement | null }>({});

  // Store submenu data state
  const [submenusData, setSubmenusData] = useState<SubmenuData[]>([]);

  // Use a single effect to fetch data for all submenus
  useEffect(() => {
    if (!menuItem?.child?.length) {
      setSubmenusData([]);
      return;
    }

    // Create initial state for all submenus
    const initialData = menuItem.child.map((submenu) => ({
      submenu,
      videos: [],
      isLoading: true,
    }));

    setSubmenusData(initialData);

    // Fetch data for each submenu
    const fetchSubmenusData = async () => {
      const promises = menuItem.child.map(async (submenu) => {
        try {
          const payload = {
            app: APP_KEY,
            menu_id: submenu.id,
            type: "latest",
          };

          const response = await postRequest<VideosListResponse>(
            API_ENDPOINTS.appVideosList,
            payload
          );

          return {
            submenu,
            videos: response?.videos?.data || [],
            isLoading: false,
          };
        } catch (error) {
          console.error("Error fetching videos for submenu", submenu.id, error);
          return {
            submenu,
            videos: [],
            isLoading: false,
          };
        }
      });

      const results = await Promise.all(promises);
      setSubmenusData(results);
    };

    fetchSubmenusData();
  }, [menuItem]);

  useEffect(() => {
    if (!menuItem?.child?.length) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const sectionId = Number(
              entry.target.getAttribute("data-section-id")
            );
            if (sectionId) {
              setActiveSubmenuId(sectionId);
            }
          }
        });
      },
      {
        root: containerRef.current,
        rootMargin: "-120px 0px -50% 0px", // Adjust threshold to account for sticky headers
        threshold: 0,
      }
    );

    // Observe all sections
    Object.entries(sectionRefs.current).forEach(([, element]) => {
      if (element) {
        observer.observe(element);
      }
    });

    return () => observer.disconnect();
  }, [menuItem?.child]);

  const handleBannerClick = (redirectUrl: string) => {
    window.open(redirectUrl, "_blank");
  };

  const scrollToSection = (submenuId: number) => {
    setActiveSubmenuId(submenuId);
    const mainHeaderHeight = 48; // Main header height
    const submenuHeight = 44; // Submenu height (py-3 = 12px * 2 + content)
    const totalOffset = mainHeaderHeight + submenuHeight;

    // Use setTimeout to ensure DOM is updated before scrolling
    setTimeout(() => {
      const element = sectionRefs.current[submenuId];
      if (element) {
        const container =
          containerRef.current?.closest(".overflow-y-auto") ||
          document.documentElement;
        if (container) {
          const elementPosition = element.getBoundingClientRect().top;
          const offsetPosition =
            elementPosition + window.pageYOffset - totalOffset;

          container.scrollTo({
            top: offsetPosition,
            behavior: "smooth",
          });
        }
      }
    }, 10);
  };

  if (isBannersLoading && isFirstTab) {
    return (
      <div className="px-4 mt-4">
        <div className="relative aspect-[16/9] bg-gray-200 animate-pulse rounded-lg" />
      </div>
    );
  }

  return (
    <div className="inset-0 flex flex-col" ref={containerRef}>
      {/* Submenu */}
      {menuItem?.child && menuItem.child.length > 0 && (
        <div className="bg-white px-4 py-3 flex gap-3 items-center overflow-x-auto flex-none shadow-sm sticky top-12 z-10">
          {menuItem.child.map((submenu) => {
            return (
              <p
                key={submenu.id}
                className={`${
                  activeSubmenuId === submenu.id
                    ? theme === "gold"
                      ? "text-textColorGold text-base"
                      : "text-textColorPrimary text-base"
                    : "text-sm"
                } whitespace-nowrap cursor-pointer`}
                onClick={() => scrollToSection(submenu.id)}
              >
                {submenu.title}
              </p>
            );
          })}
        </div>
      )}
      {/* Body */}
      <div>
        <div className="px-4 mt-2">
          {/* Swiper Banner - Header (Only shown for first tab) */}
          {isFirstTab && (
            <>
              <div className="relative">
                <Swiper
                  loop
                  autoplay
                  allowTouchMove={true}
                  indicatorProps={{
                    color: "white",
                  }}
                >
                  {bannersData?.banners.map((banner) => (
                    <Swiper.Item key={banner.id}>
                      <div
                        className="cursor-pointer relative aspect-[7/3]"
                        onClick={() => handleBannerClick(banner.redirect_url)}
                      >
                        <img
                          className="w-full h-full object-cover"
                          src={config?.config.thumb_url + banner.image_url}
                          alt={`Banner ${banner.id}`}
                        />
                        <div className="absolute w-full bottom-0 bannerDesc rounded-xl">
                          <div className="pb-2 px-5 flex items-center justify-between">
                            <p className="text-white">{banner.title}</p>
                            <p className="bg-[#9D5596] text-white px-1 py-[2px] text-[10px] rounded-[2px]">
                              广告
                            </p>
                          </div>
                        </div>
                      </div>
                    </Swiper.Item>
                  ))}
                </Swiper>
              </div>
              {/* Marquee (Also only shown for first tab) */}
              <MarqueeNoticeBar />
            </>
          )}
          {/* Content Sections */}
          {submenusData.map(({ submenu, videos, isLoading }) => (
            <div
              key={submenu.id}
              ref={(el) => (sectionRefs.current[submenu.id] = el)}
              data-section-id={submenu.id}
              className="mb-8"
            >
              <SubcategoryContent
                id={submenu.id}
                title={submenu.title}
                categoryId={submenu.video_category_id}
                videos={videos}
                isLoading={isLoading}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default HomeContent;
