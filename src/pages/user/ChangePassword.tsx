import { Button, Form, Input, Modal, Toast } from "antd-mobile";
import Navbar from "../../components/Navbar";
import { useState } from "react";
import { useUpdatePassword } from "../../hooks/useAuth";
import { useNavigate } from "react-router";

const ChangePassword = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [newPasswordVisible, setNewPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);

  const { mutate: updatePassword, isPending } = useUpdatePassword();

  const handleFormOnFinish = (values: any) => {
    updatePassword(
      {
        oldPassword: values.password,
        newPassword: values.new_password,
        passwordConfirmation: values.confirm_password,
      },
      {
        onSuccess: (res) => {
          console.log(res);
          if (res.code === 1) {
            Toast.show({
              content: res.message || "修改密码失败，请重试",
            });
          } else {
            Modal.alert({
              title: "消息提示",
              content: (
                <>
                  <div className="my-0 mx-auto text-center w-[80%]">
                    <p className="text-disabledIconGrey text-sm">
                      新的密码已经修改完成, 请下一次登陆 以新密码进行登陆!
                    </p>
                  </div>
                </>
              ),
              confirmText: "确定",
              bodyClassName: "lg:w-[80%] my-0 mx-auto hintModalClassName",
              onConfirm: () => {
                // reset form
                form.resetFields();
              },
            });
          }
        },
        onError: (error) => {
          Toast.show({
            content: error.message || "修改密码失败，请重试",
          });
        },
      }
    );
  };

  return (
    <div className="h-dvh">
      <Navbar title="修改密码" />
      <div className="pt-12"></div>
      <div className="p-5">
        <div>
          <Form
            form={form}
            layout="horizontal"
            onFinish={handleFormOnFinish}
            className="mmForm"
            footer={
              <Button
                block
                type="submit"
                loading={isPending}
                className="primayBtn w-full"
              >
                确认修改
              </Button>
            }
          >
            {/* 当前密码 */}
            <div className="flex items-center gap-2 justify-center w-full border-b">
              <img
                className="w-4 h-4 object-contain"
                src="/assets/images/password-lock.png"
                alt="lock"
              />
              <Form.Item
                name="password"
                rules={[
                  { required: true, message: "当前密码不能为空" },
                  { min: 6, message: "密码长度不能小于6位" },
                ]}
              >
                <Input
                  type={passwordVisible ? "text" : "password"}
                  placeholder="请输入当前密码"
                  className="placeholder-shown:border-gray-50 sm-input-placeholder w-full pl-0"
                />
              </Form.Item>
              <img
                className="w-4 h-4 object-contain cursor-pointer"
                src={
                  passwordVisible
                    ? "/assets/images/show.png"
                    : "/assets/images/no-show.png"
                }
                alt="back"
                onClick={() => setPasswordVisible((prev) => !prev)}
              />
            </div>
            {/* 新的密码 */}
            <div className="flex items-center gap-2 justify-center w-full border-b">
              <img
                className="w-4 h-4 object-contain"
                src="/assets/images/single-lock.png"
                alt="lock"
              />
              <Form.Item
                name="new_password"
                rules={[
                  { required: true, message: "新的密码不能为空" },
                  { min: 6, message: "密码长度不能小于6位" },
                ]}
              >
                <Input
                  type={newPasswordVisible ? "text" : "password"}
                  placeholder="请输入新的密码"
                  className="placeholder-shown:border-gray-50 sm-input-placeholder w-full pl-0"
                />
              </Form.Item>
              <img
                className="w-4 h-4 object-contain cursor-pointer"
                src={
                  newPasswordVisible
                    ? "/assets/images/show.png"
                    : "/assets/images/no-show.png"
                }
                alt="back"
                onClick={() => setNewPasswordVisible((prev) => !prev)}
              />
            </div>
            {/* 确认新的密码 */}
            <div className="flex items-center gap-2 justify-center w-full">
              <img
                className="w-4 h-4 object-contain"
                src="/assets/images/double-lock.png"
                alt="lock"
              />
              <Form.Item
                name="confirm_password"
                rules={[
                  { required: true, message: "确认新的密码不能为空" },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue("new_password") === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error("两次输入的密码不一致"));
                    },
                  }),
                ]}
              >
                <Input
                  type={confirmPasswordVisible ? "text" : "password"}
                  placeholder="请确认新的密码"
                  className="placeholder-shown:border-gray-50 sm-input-placeholder w-full pl-0"
                />
              </Form.Item>
              <img
                className="w-4 h-4 object-contain cursor-pointer"
                src={
                  confirmPasswordVisible
                    ? "/assets/images/show.png"
                    : "/assets/images/no-show.png"
                }
                alt="back"
                onClick={() => setConfirmPasswordVisible((prev) => !prev)}
              />
            </div>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default ChangePassword;
