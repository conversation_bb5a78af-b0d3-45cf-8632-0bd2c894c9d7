import Navbar from "../../components/Navbar";

const MemberShowOrder = () => {
  return (
    <div className="h-dvh">
      {/* Navbar */}
      <div className="relative">
        <Navbar title="会员晒单" />
        <img
          src="/assets/images/bubble.png"
          alt="chat"
          className="w-[22px] absolute top-1/2 right-3 -translate-y-1/2"
        />
      </div>

      {/* Body */}
      <div className="my-3">
        {/* Post */}
        <div className="px-2 ">
          <div className="bg-white px-3 py-2 rounded-md">
            {/* Profile */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <img
                  src="/assets/images/profile-img.png"
                  alt="profile"
                  className="w-10"
                />
                <div>
                  <p className="text-sm text-disabledIconGrey">TEXT***123</p>
                  <p className="text-xs text-white bg-iconColorPrimary rounded px-1 py-[1px]">
                    永久VIP会员
                  </p>
                </div>
              </div>
              <div className="text-right text-xs text-disabledIconGrey/70">
                <p className="my-1">2024.08.01</p>
                <p>已兑换福利-清纯充气娃娃一个</p>
              </div>
            </div>
            {/* Content */}
            <div className="my-3">
              <p className="text-xs text-disabledIconGrey">
                用户评价：非常完美的一次体验！兑换成功后直接联系客服给到地址，客服的服务让我十分的满意且高效！货物已经拿到了，让我有了全新的体验，给猫咪官方点赞！
              </p>
              <div className="flex items-center gap-3 flex-wrap my-3">
                <img
                  src="/assets/images/parcel.png"
                  alt="item"
                  className="w-[80px]"
                />
                <img
                  src="/assets/images/parcel.png"
                  alt="item"
                  className="w-[80px]"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MemberShowOrder;
