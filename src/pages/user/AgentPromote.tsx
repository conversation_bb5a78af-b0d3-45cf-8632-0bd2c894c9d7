import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, To<PERSON> } from "antd-mobile";
import { useState } from "react";
import { NavLink, useNavigate } from "react-router";

const AgentPromote = () => {
  const navigate = useNavigate();

  const isVip: number = 1;
  const [rulesModalVisible, setRulesModalVisible] = useState(false);
  const [sharePromotePopupVisible, setSharePromotePopupVisible] =
    useState(false);
  const [shareToSocialMediaPopupVisible, setShareToSocialMediaPopupVisible] =
    useState(false);

  const handleCopyLink = (values: any) => {
    console.log("handleCopyLink", values);
    Toast.show({
      content: "复制成功",
      position: "top",
    });
  };

  const handleSharePromote = (values: any) => {
    console.log("handleSharePromote", values);
    setSharePromotePopupVisible(false);
    setShareToSocialMediaPopupVisible(true);
  };

  return (
    <>
      <div>
        <div className="relative bg-gradientColorPrimary1 h-[5vh]">
          <div className="flex items-center w-full absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
            {/* Navbar - Back Icon */}
            <div
              className="w-4 h-4 absolute top-1/2 left-[20px] -translate-x-1/2 -translate-y-1/2"
              onClick={() => {
                navigate(-1);
              }}
            >
              <img
                className="w-full h-full object-contain"
                src="/assets/images/arrow-left.png"
                alt="back"
              />
            </div>
            {/* Navbar - Title */}
            <div className="flex-[11] text-center text-xl text-white">
              <h2>代理推广</h2>
            </div>
          </div>
        </div>
        {/* Header */}
        <div>
          <div className="meHeaderBg h-[25vh] px-4 pt-4">
            {/* Avatar & exchange */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-[60px]">
                  <img src="/assets/images/profile.png" alt="avatar" />
                </div>
                <div className="text-white">
                  <p className="text-base">TEXT123</p>
                  <p>
                    {isVip === 2
                      ? "永久VIP会员"
                      : isVip === 1
                      ? "VIP会员"
                      : "普通会员"}
                  </p>
                  <p>猫咪ID:338981</p>
                </div>
              </div>
              <div className="flex flex-col gap-2">
                <div className="flex items-center text-white justify-between px-1">
                  <p className="text-sm">可兑换积分</p>
                  <p className="text-base">0</p>
                </div>
                <div className="w-[140px] text-white">
                  <button
                    className={`w-full ${
                      isVip === 0
                        ? "bg-disabledIconGrey"
                        : "bg-iconColorPrimary"
                    } py-2 px-3 rounded-lg shadow-3xl`}
                    onClick={() => navigate("/me/points-redemption")}
                  >
                    立即兑换
                  </button>
                </div>
              </div>
            </div>
            {/* 兑换记录 */}
            <div className="flex items-center justify-between my-3">
              <p className="text-xs text-textColorPrimaryDark font-semibold">
                {isVip === 0
                  ? "*你还未成为会员，以下链接无法点击，请先成为会员"
                  : ""}
              </p>
              <NavLink to="/me/exchange-record">
                <p className="text-white underline">兑换记录</p>
              </NavLink>
            </div>
            {/* 分享/推广 按钮 */}
            <div className="flex gap-5">
              <button
                className={`w-1/2 ${
                  isVip === 0 ? "bg-disabledIconGrey" : "bg-iconColorPrimary"
                } py-2 px-3 rounded-lg shadow-3xl text-white`}
                onClick={() => setSharePromotePopupVisible(true)}
              >
                分享推广
              </button>
              <button
                className={`w-1/2 ${
                  isVip === 0 ? "bg-disabledIconGrey" : "bg-iconColorPrimary"
                } py-2 px-3 rounded-lg shadow-3xl text-white`}
                onClick={() => navigate("/me/promotional-data")}
              >
                推广数据
              </button>
            </div>
          </div>
          {/* 操作 和 福利兑换 说明 */}
          <div className="h-[70vh] px-4">
            {/* 操作说明 */}
            <div className="relative bg-white my-2 border-[0.5px] border-disabledGrey/50 p-3">
              <div className="text-center text-sm font-semibold">
                <p>操作简单，奖品多多</p>
              </div>
              <p
                className="absolute top-2 right-3 underline text-textColorPrimary"
                onClick={() => {
                  setRulesModalVisible(true);
                }}
              >
                规则说明
              </p>
              <div className="text-xs mx-2">
                <p className="">操作说明</p>
                <ul>
                  <li className="my-2">
                    1.依次点击【我的】-【代理推广】-【分享推广】获取专属
                    推广链接,邀请码或二维码
                  </li>
                  <li className="my-2">
                    2.在首页弹窗点击【立即前往】根据以上步骤邀请
                  </li>
                </ul>
              </div>
            </div>
            {/* 福利兑换 说明 */}
            <div className="relative bg-white my-2 border-[0.5px] border-disabledGrey/50 pt-3">
              <div className="text-center text-sm font-semibold mb-2">
                <p>福利兑换说明</p>
              </div>
              <div className="grid grid-cols-4 text-center text-xs">
                <p className="py-2 border-b-[0.5px] border-disabledGrey/40">
                  福利名称
                </p>
                <p className="py-2 border-b-[0.5px] border-disabledGrey/40">
                  积分要求
                </p>
                <p className="col-span-2 py-2 border-b-[0.5px] border-disabledGrey/40">
                  条件
                </p>
                <p className="my-3 border-b-[0.5px] border-disabledGrey/40">
                  高端型福利
                </p>
                <p className="my-3 border-b-[0.5px] border-disabledGrey/40">
                  10000积分
                </p>
                <div className="row-span-4 col-span-2 border-l-[0.5px] border-disabledGrey/40 px-2">
                  <p className="my-3 text-left">1元=1积分；</p>
                  <p className="mt-4 mb-5 text-left">
                    例：会员A邀请会员B注册成功，且会员B在当日充值30元，则会员A获得30积分；
                  </p>
                  <p className="py-3 text-textColorPrimary text-left">
                    被邀请注册的会员，需在注册后的7天内充值算做有效期，注册7天后的充值不计算在积分内;
                  </p>
                </div>
                <p className="my-3 border-b-[0.5px] border-disabledGrey/40">
                  高级型福利
                </p>
                <p className="my-3 border-b-[0.5px] border-disabledGrey/40">
                  5000积分
                </p>
                <p className="my-3 border-b-[0.5px] border-disabledGrey/40">
                  中级型福利
                </p>
                <p className="my-3 border-b-[0.5px] border-disabledGrey/40">
                  1000积分
                </p>
                <p className="my-3">入门型福利</p>
                <p className="my-3">1000积分</p>
              </div>
            </div>
            {/* 会员出单 立即推广 按钮 */}
            <div className="mt-4 pb-8 flex flex-col gap-4">
              <Button
                block
                className="primayIconBtn w-full"
                onClick={() => {
                  navigate("/me/member-show-order");
                }}
              >
                会员晒单
              </Button>
              <Button block className="disabledBtn w-full">
                立即推广
              </Button>
            </div>
          </div>
        </div>
      </div>
      {/* 规则说明 弹窗 */}
      <Modal
        visible={rulesModalVisible}
        image="/assets/images/actor-1.png"
        className="customRulesModal"
        title=" "
        style={{
          width: "80vw",
        }}
        content={
          <div className="px-4">
            <ul className="text-sm text-disabledIconGrey ">
              <li className="my-4">
                <p>
                  1、邀请至少1名以上的好友成功注册并成为付费会员即获得积分返利(1元=1积分，如邀请好友一人,在注册后当日充值30元,邀请人即可获得30积分返利)
                  ;
                </p>
              </li>
              <li className="my-4">
                <p>2、仅注册并未充值的会员不计算在返还积分内;</p>
              </li>
              <li className="my-4">
                <p>
                  3、被邀请人需在注册页面内填写您的邀请码,否则不计算在邀请人数内;
                </p>
              </li>
              <li className="my-4">
                <p>4、邀请的账户必须为VIP会员;</p>
              </li>
              <li className="my-4">
                <p>
                  5、被邀请的会员需在注册后的7天内充值算作有效期,注册7天后的充值则不计算在积分内;
                </p>
              </li>
              <li className="my-4">
                <p>
                  6、获得的积分将永久有效的保存在账户内,兑换商品成功后需立刻联系在线客服提供邮寄地址,否则将视作无效!
                </p>
              </li>
              <li className="my-4">
                <p>
                  7、商品成功发货后,会将快递单号发送至个人中心-消息中心，烦请会员留意；
                </p>
              </li>
              <li className="my-4">
                <p>
                  8、如发现有作弊、恶意刷量等违规行为,本站有权取消相关用户的活动资格并追回已发放的奖励。
                </p>
              </li>
              <li className="my-4">
                <p>9、活动最终解释权归本站所有。</p>
              </li>
            </ul>
          </div>
        }
        closeOnAction
        showCloseButton={true}
        onClose={() => {
          setRulesModalVisible(false);
        }}
        actions={[
          {
            key: "confirm",
            text: "确认",
          },
        ]}
      />
      {/* 分享推广 */}
      <Popup
        visible={sharePromotePopupVisible}
        // showCloseButton
        // onClose={() => {
        //   setSharePromotePopupVisible(false);
        // }}
        onMaskClick={() => {
          setSharePromotePopupVisible(false);
        }}
        bodyStyle={{
          borderTopLeftRadius: "10px",
          borderTopRightRadius: "10px",
          minHeight: "40vh",
        }}
        className="bottomPopup lg:max-w-[30vw]"
      >
        {/* Header */}
        <div>
          <div className="bg-iconColorPrimary py-3 text-center text-white">
            <div className="relative">
              <p className="text-lg">分享推广</p>
              <img
                src="/assets/images/share.png"
                className="w-5 h-5 absolute top-0 right-3"
                alt="share"
                onClick={() => handleSharePromote("")}
              />
            </div>
            <div className="w-[150px] mt-4 mx-auto">
              <img
                src="/assets/images/logo-white.png"
                className="w-full"
                alt="logo"
              />
            </div>
            <p className="text-xl">遇见下一个X站时代</p>
          </div>
        </div>
        {/* Body */}
        <div className="py-2">
          <p className="text-base text-center text-disabledIconGrey">
            累计邀请有效用户 0 人
          </p>
          {/* Qr code */}
          <div className="px-5 pt-2 pb-2 border border-disabledIconGrey/50 w-[280px] my-3 mx-auto rounded-lg text-center">
            <img
              src="/assets/images/qr-code.png"
              className="w-[150px] h-[150px] mt-0 mb-2 mx-auto"
              alt="qr-code"
            />
            <p className="text-base text-disabledIconGrey">我的邀请码</p>
            <p className="text-base text-disabledIconGrey">TEXT123</p>
          </div>
        </div>
        {/* Footer */}
        <div className="flex items-center gap-3 px-3 pb-5">
          <button className="flex-1 bg-iconColorPrimary py-2 text-white rounded shadow-3xl">
            保存图片
          </button>
          <button
            className="flex-1 bg-iconColorPrimary py-2 text-white rounded shadow-3xl"
            onClick={() => handleCopyLink("copy")}
          >
            复制链接
          </button>
        </div>
      </Popup>
      {/* 分享到 - 微信/QQ */}
      <Popup
        visible={shareToSocialMediaPopupVisible}
        onMaskClick={() => {
          setShareToSocialMediaPopupVisible(false);
        }}
        bodyStyle={{
          borderTopLeftRadius: "10px",
          borderTopRightRadius: "10px",
          minHeight: "22vh",
        }}
        className="bottomPopup lg:max-w-[30vw]"
      >
        {/* Header */}
        <div className="bg-iconColorPrimary py-3 text-center text-white">
          <div className="relative">
            <p className="text-lg">分享到</p>
            <img
              src="/assets/images/arrow-left.png"
              className="w-3 absolute top-1 left-5"
              alt="back"
              onClick={() => {
                setShareToSocialMediaPopupVisible(false);
                setSharePromotePopupVisible(true);
              }}
            />
          </div>
        </div>
        {/* Body */}
        <div className="flex items-center justify-around py-3">
          <div className="text-center">
            <img
              src="/assets/images/wechat.png"
              alt="wechat"
              className="w-12 my-1 mx-auto"
            />
            <p>分享到微信</p>
          </div>
          <div className="text-center">
            <img
              src="/assets/images/wechat.png"
              alt="wechat"
              className="w-12 my-1 mx-auto"
            />
            <p>分享到微信</p>
          </div>
          <div className="text-center">
            <img
              src="/assets/images/wechat.png"
              alt="wechat"
              className="w-12 my-1 mx-auto"
            />
            <p>分享到微信</p>
          </div>
        </div>
      </Popup>
    </>
  );
};

export default AgentPromote;
