import { Button, Form, Input, Toast } from "antd-mobile";
import HeroHeader from "../../components/HeroHeader";
import Navbar from "../../components/Navbar";
import { useState } from "react";
import { useNavigate } from "react-router";
import TermAndConditionModal from "../../components/TermAndConditionModal";
import { useRegister } from "../../hooks/useAuth";
import { ThemeColor } from "@/components/ThemeColor";
import { useUserInfo } from "../../hooks/useAuth";
import { useUser } from "../../contexts/UserContext";
import { useLocation } from "react-router";

interface RegisterFormValues {
  username: string;
  new_password: string;
  confirm_password: string;
}

const Register = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [newPasswordVisible, setNewPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);
  const [termConditionsModalVisible, setTermConditionsModalVisible] =
    useState(false);
  const [isTermsAccepted, setIsTermsAccepted] = useState(false);

  const { mutate: register, isPending } = useRegister();
  const { refetch: fetchUserInfo } = useUserInfo(false);
  const { setUser } = useUser();
  const location = useLocation();

  const handleFormOnFinish = async (values: RegisterFormValues) => {
    if (!isTermsAccepted) {
      Toast.show({
        content: "请先同意隐私条款",
        position: "top",
      });
      return;
    }

    register(
      {
        username: values.username,
        password: values.new_password,
        passwordConfirmation: values.confirm_password,
      },
      {
        onSuccess: async (response) => {
          console.log("response", response);
          if (response.access_token) {
            localStorage.setItem("access_token", response.access_token);

            try {
              const userInfoResponse = await fetchUserInfo();
              if (userInfoResponse.data) {
                setUser(userInfoResponse.data);
              }

              Toast.show({
                icon: "success",
                content: "注册并登录成功",
              });

              const from = location.state?.from?.pathname || "/";
              navigate(from, { replace: true });
            } catch (error) {
              console.error(
                "Failed to fetch user info after registration:",
                error
              );
              Toast.show({
                icon: "fail",
                content: "注册成功，但自动登录失败，请手动登录。",
              });
              navigate("/login");
            }
          } else {
            Toast.show({
              icon: "fail",
              content:
                response.message || "注册成功，但未返回令牌，请手动登录。",
            });
            navigate("/login");
          }
        },
        onError: (error) => {
          Toast.show({
            content: error.message || "注册失败，请重试",
            position: "top",
          });
        },
      }
    );
  };

  return (
    <>
      <div className="bg-white h-dvh relative pt-12">
        <ThemeColor color="primary" />
        {/* Navbar */}
        <Navbar title="用户注册" />
        <HeroHeader />
        <div className="bg-white"></div>
        <Form
          form={form}
          layout="horizontal"
          className="p-7 loginForm"
          onFinish={handleFormOnFinish}
        >
          {/* 用户名 */}
          <div className="flex items-center gap-2 border-b border-gradientColorPrimary1 px-2 my-2">
            <img
              className="w-4 h-4 object-contain"
              src="/assets/images/user.png"
              alt="user"
            />
            <Form.Item
              name="username"
              rules={[
                { required: true, message: "请输入用户名" },
                { min: 3, message: "用户名至少3个字符" },
              ]}
              className="mb-2"
            >
              <Input
                placeholder="请输入用户名"
                className="base-input-placeholder"
              />
            </Form.Item>
          </div>
          {/* 新密码 */}
          <div className="flex items-center gap-2 border-b border-gradientColorPrimary1 px-2 my-2">
            <img
              className="w-4 h-4 object-contain"
              src="/assets/images/lock.png"
              alt="lock"
            />
            <Form.Item
              name="new_password"
              rules={[
                { required: true, message: "请输入新密码" },
                { min: 6, message: "密码至少6个字符" },
              ]}
              className="mb-2 w-full"
            >
              <Input
                type={newPasswordVisible ? "text" : "password"}
                placeholder="输入新密码"
                className="base-input-placeholder"
              />
            </Form.Item>
            <img
              className="w-4 h-4 object-contain cursor-pointer"
              src={
                newPasswordVisible
                  ? "/assets/images/show.png"
                  : "/assets/images/no-show.png"
              }
              alt="back"
              onClick={() => setNewPasswordVisible((prev) => !prev)}
            />
          </div>
          {/* 确认密码 */}
          <div className="flex items-center gap-2 border-b border-gradientColorPrimary1 px-2 my-2">
            <img
              className="w-4 h-4 object-contain"
              src="/assets/images/lock.png"
              alt="lock"
            />
            <Form.Item
              name="confirm_password"
              rules={[
                { required: true, message: "请确认密码" },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue("new_password") === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error("两次输入的密码不一致"));
                  },
                }),
              ]}
              className="mb-2 w-full"
            >
              <Input
                type={confirmPasswordVisible ? "text" : "password"}
                placeholder="请确认密码"
                className="base-input-placeholder"
              />
            </Form.Item>
            <img
              className="w-4 h-4 object-contain cursor-pointer"
              src={
                confirmPasswordVisible
                  ? "/assets/images/show.png"
                  : "/assets/images/no-show.png"
              }
              alt="back"
              onClick={() => setConfirmPasswordVisible((prev) => !prev)}
            />
          </div>
          <div className="mt-12 lg:mt-[80px] mb-1 lg:mb-3 text-center">
            <p className="text-xs text-disabledIconGrey">
              *您可在注册之后再选择是否绑定手机号码
            </p>
          </div>
          <Form.Item>
            <Button
              fill="solid"
              className="gradientBtn w-full"
              type="submit"
              loading={isPending}
              disabled={isPending}
            >
              确认注册
            </Button>
          </Form.Item>
        </Form>
        <div className="flex justify-center gap-2">
          <p className="text-disabledGrey text-base">已有账号?</p>
          <p
            className="text-textColorPrimary text-base underline"
            onClick={() => navigate("/login")}
          >
            点击登录
          </p>
        </div>
        <div className="absolute bottom-3 w-full">
          <div className="text-center flex items-center justify-center gap-2">
            <input
              type="checkbox"
              checked={isTermsAccepted}
              onChange={(e) => setIsTermsAccepted(e.target.checked)}
            />
            <p className="text-disabledIconGrey">
              我已阅读同意
              <span
                className="text-textColorPrimary underline"
                onClick={() => setTermConditionsModalVisible(true)}
              >
                隐私条款
              </span>
            </p>
          </div>
        </div>
      </div>
      <TermAndConditionModal
        visible={termConditionsModalVisible}
        setVisible={setTermConditionsModalVisible}
      />
    </>
  );
};

export default Register;
