import { useMemberTopics } from "@/hooks/useApp";
import Empty from "../../components/Empty";
import Navbar from "../../components/Navbar";
import { MemberTopicsResponse } from "@/types/member";
import { useNavigate } from "react-router";
const PurchasedTheme = () => {
  const { data } = useMemberTopics();
  console.log(data);
  const navigate = useNavigate();
  return (
    <div className="h-dvh">
      <Navbar title="已购买主题" />
      {data?.app_member_topic && data.app_member_topic.length > 0 ? (
        <div className="pt-12">
          {/* Header */}
          <div className="flex items-center text-center py-3 text-disabledIconGrey">
            <p className="flex-1">主题名称</p>
            <p className="flex-1">在线观看</p>
            {/* <p className="flex-1">下载捆绑包</p> */}
          </div>
          {/* Content list */}
          {data.app_member_topic.map((item) => {
            return (
              <div
                key={item.id}
                className="flex items-center text-center py-3 bg-white text-xs border-b-[0.5px] border-disabledGrey/40"
              >
                <p className="flex-1 text-disabledIconGrey">{item.title}</p>
                {item.paid_at ? (
                  <p
                    className="flex-1 text-[#D64C65] underline cursor-pointer"
                    onClick={() => navigate(`/theme/detail/${item.topic_uid}`)}
                  >
                    在线观看
                  </p>
                ) : (
                  // If not paid, show "前往支付" link
                  <p
                    className="flex-1 text-iconColorPrimary underline cursor-pointer"
                    onClick={() =>
                      navigate(
                        `/me/payment/${item.product_id}?isTopic=true&price=${item.amount}`
                      )
                    }
                  >
                    前往支付
                  </p>
                )}
                {/* <p className="flex-1 text-textColorPrimary underline">
                  下载捆绑包
                </p> */}
              </div>
            );
          })}
          {/* Footer */}
          <p className="text-center my-5 text-disabledGrey text-base">
            以上为所有的消费记录
          </p>
        </div>
      ) : (
        <div className="pt-12">
          <Empty />
        </div>
      )}
    </div>
  );
};

export default PurchasedTheme;
