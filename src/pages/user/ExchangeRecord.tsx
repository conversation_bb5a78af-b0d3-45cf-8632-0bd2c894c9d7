import Empty from "../../components/Empty";
import Navbar from "../../components/Navbar";

const records = [
  {
    id: 1,
    date: "2024.09.25",
    points: "880积分",
    name: "三田真铃假臀",
  },
  {
    id: 2,
    date: "2024.09.25",
    points: "770积分",
    name: "持久不射喷雾",
  },
  {
    id: 3,
    date: "2024.09.25",
    points: "12800积分",
    name: "欧美充气娃娃",
  },
];
const ExchangeRecord = () => {
  return (
    <div className="h-dvh">
      <Navbar title="兑换记录" />
      {/* Header */}
      {records?.length > 0 ? (
        <>
          <div className="flex items-center text-center py-4 bg-unselectedButtonColor text-xs border-b text-disabledIconGrey">
            <p className="flex-1">时间</p>
            <p className="flex-1">消耗积分</p>
            <p className="flex-1">兑换物品</p>
          </div>
          {/* Body */}
          <div className="bg-white text-disabledIconGrey">
            {records?.map((item) => {
              return (
                <div
                  className="flex items-center text-center py-4  border-b"
                  key={item?.id}
                >
                  <p className="flex-1">{item?.date}</p>
                  <p className="flex-1">{item?.points}</p>
                  <p className="flex-1 underline text-textColorPrimary">
                    {item?.name}
                  </p>
                </div>
              );
            })}
          </div>
          {/* Footer */}
          <p className="text-center my-5 text-disabledGrey text-base">
            以上为所有的消费记录
          </p>
        </>
      ) : (
        <Empty />
      )}
    </div>
  );
};

export default ExchangeRecord;
