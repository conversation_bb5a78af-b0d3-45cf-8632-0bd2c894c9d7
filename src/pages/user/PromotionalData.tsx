import { useNavigate } from "react-router";
import Navbar from "../../components/Navbar";

const PromotionalData = () => {
  const navigate = useNavigate();
  return (
    <div className="h-dvh">
      <Navbar
        title="推广数据"
        addOnAfter={
          <img
            src="/assets/images/bubble.png"
            alt="chat"
            className="w-[22px] absolute top-1/2 right-3 -translate-y-1/2"
          />
        }
      />
      {/* 可兑换积分 */}
      <div className="bg-white py-5 my-5 mx-3 border-[0.5px]">
        <div className="flex items-center gap-5 py-5 px-3">
          <div className="border-r text-center flex-1 text-disabledIconGrey">
            <p className="mb-12 mt-8">可兑换积分</p>
            <div className="mb-12 flex items-center justify-center gap-2">
              <p className="text-lg text-textColorPrimary">0</p>
              <p>积分</p>
            </div>
          </div>
          <div className="text-center flex-1 text-disabledIconGrey">
            <p className="mb-12 mt-8">总获得积分</p>
            <div className="mb-12 flex items-center justify-center gap-2">
              <p className="text-lg text-textColorPrimary">0</p>
              <p>积分</p>
            </div>
          </div>
        </div>
        <div className="flex justify-center">
          <button
            className="w-1/2 bg-iconColorPrimary py-2 px-3 rounded-lg shadow-3xl text-white"
            onClick={() => navigate("/me/points-redemption")}
          >
            立即兑换
          </button>
        </div>
      </div>
      {/* 昨日 / 当月总积分 */}
      <div className="bg-white py-2 my-5 mx-3 border-[0.5px]">
        <div className="flex items-center gap-5 pb-3 px-3 border-b">
          <div className="text-center flex-1 text-disabledIconGrey">
            <p className="mb-3 mt-2">昨日积分</p>
            <div className="mb-3 flex items-center justify-center gap-2">
              <p className="text-lg text-textColorPrimary">0</p>
              <p>积分</p>
            </div>
          </div>
          <div className="text-center flex-1 text-disabledIconGrey">
            <p className="mb-3 mt-2">当月总积分</p>
            <div className="mb-3 flex items-center justify-center gap-2">
              <p className="text-lg text-textColorPrimary">0</p>
              <p>积分</p>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-5 pt-3 px-3">
          <div className="text-center flex-1 text-disabledIconGrey">
            <p className="mb-3 mt-2">昨日推广人数</p>
            <div className="mb-3 flex items-center justify-center gap-2">
              <p className="text-lg text-textColorPrimary">0</p>
              <p>积分</p>
            </div>
          </div>
          <div className="text-center flex-1 text-disabledIconGrey">
            <p className="mb-3 mt-2">当月推广人数</p>
            <div className="mb-3 flex items-center justify-center gap-2">
              <p className="text-lg text-textColorPrimary">0</p>
              <p>积分</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PromotionalData;
