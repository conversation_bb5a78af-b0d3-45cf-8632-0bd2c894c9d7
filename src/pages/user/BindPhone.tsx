import { Button, Form, Input, Modal, Toast } from "antd-mobile";
import Navbar from "../../components/Navbar";
import CountryCodeDropdown from "../../components/CountryCodeDropdown";
import { useState, useCallback } from "react";
import { useSendVerificationCode, useBindPhone } from "../../hooks/useAuth";

interface FormValues {
  phone: string;
  code: string;
}

const BindPhone = () => {
  const [form] = Form.useForm<FormValues>();
  const [selectedValue, setSelectedValue] = useState("+86");
  const [cooldown, setCooldown] = useState(0);

  const { mutate: sendVerificationCode, isPending: isSendingCode } =
    useSendVerificationCode();
  const { mutate: bindPhone, isPending: isBinding } = useBindPhone();

  const startCooldown = useCallback(() => {
    setCooldown(60);
    const timer = setInterval(() => {
      setCooldown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  }, []);

  const handleFormOnFinish = (values: FormValues) => {
    const { phone, code } = values;

    bindPhone(
      {
        countryCode: selectedValue,
        phone,
        code,
      },
      {
        onSuccess: (res) => {
          console.log(res);
          if (res.code == 1) {
            Toast.show({
              content: res.message || "绑定手机号失败，请重试",
            });
          } else {
            Toast.show({
              content: "手机号绑定成功",
            });
          }
        },
        onError: (error) => {
          Toast.show({
            content: error.message || "绑定手机号失败，请重试",
          });
        },
      }
    );
  };

  const handleDropdownValue = (values: string) => {
    setSelectedValue(values);
  };

  const validatePhone = (phone: string): boolean => {
    return /^\d{6,}$/.test(phone);
  };

  const handleGetVerificationCode = async (
    e: React.MouseEvent<HTMLButtonElement>
  ) => {
    e.preventDefault();
    const phone = form.getFieldValue("phone");

    if (!phone) {
      Toast.show({
        content: "请输入手机号码",
      });
      return;
    }

    if (!validatePhone(phone)) {
      Toast.show({
        content: "请输入有效的手机号码",
      });
      return;
    }

    await new Promise<void>((resolve) => {
      Modal.confirm({
        title: "消息提示",
        content: (
          <>
            <div className="my-0 mx-auto text-center w-[80%]">
              <p className="text-disabledIconGrey text-sm">
                是否向此手机号发送验证码?
              </p>
            </div>
          </>
        ),
        confirmText: "确定",
        bodyClassName: "lg:w-[80%] my-0 mx-auto confirmModalClassName",
        onConfirm: () => {
          sendVerificationCode(
            {
              countryCode: selectedValue,
              phone,
            },
            {
              onSuccess: (res) => {
                if (res.code === 1) {
                  Toast.show({
                    content: res.message || "发送验证码失败，请重试",
                  });
                  resolve();
                } else {
                  Toast.show({
                    content: "验证码已发送",
                  });
                  startCooldown();
                  resolve();
                }
              },
              onError: (error) => {
                Toast.show({
                  content: error.message || "发送验证码失败，请重试",
                });
                resolve();
              },
            }
          );
        },
      });
    });
  };

  return (
    <div className="h-dvh">
      <Navbar title="绑定手机号码" />
      <div className="pt-12">
        <div className="p-5">
          <Form
            form={form}
            layout="horizontal"
            onFinish={handleFormOnFinish}
            className="mmForm"
          >
            <div className="flex items-center gap-2 border-b border-gradientColorPrimary1 px-2 my-2">
              <img
                className="w-4 h-4 object-contain"
                src="/assets/images/user.png"
                alt="user"
              />
              <CountryCodeDropdown
                onChange={handleDropdownValue}
                defaultValue={selectedValue}
              />
              <Form.Item
                name="phone"
                rules={[
                  { required: true, message: "请输入手机号码" },
                  {
                    validator: (_, value) =>
                      validatePhone(value)
                        ? Promise.resolve()
                        : Promise.reject(new Error("请输入有效的手机号码")),
                  },
                ]}
                className="mb-2"
              >
                <Input
                  placeholder="请输入手机号码"
                  className="base-input-placeholder"
                />
              </Form.Item>
            </div>
            <div className="flex items-center gap-2 border-b border-gradientColorPrimary1 px-2 my-2">
              <img
                className="w-4 h-4 object-contain"
                src="/assets/images/verified.png"
                alt="verified"
              />
              <Form.Item
                name="code"
                rules={[{ required: true, message: "请输入验证码" }]}
                className="mb-2 flex verifiedInput"
              >
                <Input
                  placeholder="请输入验证码"
                  className="base-input-placeholder"
                />
              </Form.Item>
              <Button
                loading={isSendingCode}
                onClick={handleGetVerificationCode}
                fill="none"
                className="gradientBtn w-1/3"
                size="mini"
                disabled={cooldown > 0}
              >
                {isSendingCode
                  ? "发送中..."
                  : cooldown > 0
                  ? `${cooldown}秒后重试`
                  : "获取验证码"}
              </Button>
            </div>
            <div className="mt-8">
              <Button
                loading={isBinding}
                fill="solid"
                className="gradientBtn w-full"
                type="submit"
              >
                确认绑定
              </Button>
            </div>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default BindPhone;
