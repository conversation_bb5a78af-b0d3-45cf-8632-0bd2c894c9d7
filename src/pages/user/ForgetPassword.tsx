import { Button, Form, Input, Modal, Toast } from "antd-mobile";
import HeroHeader from "../../components/HeroHeader";
import Navbar from "../../components/Navbar";
import CountryCodeDropdown from "../../components/CountryCodeDropdown";
import { useState, useCallback } from "react";
import { useNavigate } from "react-router";
import { useSendResetCode, useResetForgotPassword } from "../../hooks/useAuth";

interface FormValues {
  phone: string;
  code: string;
  new_password: string;
  confirm_password: string;
}

const ForgetPassword = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm<FormValues>();

  const [selectedValue, setSelectedValue] = useState("+86");
  const [newPasswordVisible, setNewPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);
  const [cooldown, setCooldown] = useState(0);

  const { mutate: sendResetCode, isPending: isSendingCode } =
    useSendResetCode();
  const { mutate: resetPassword, isPending: isResetting } =
    useResetForgotPassword();

  const startCooldown = useCallback(() => {
    setCooldown(60);
    const timer = setInterval(() => {
      setCooldown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  }, []);

  const handleFormOnFinish = (values: FormValues) => {
    const { phone, code, new_password, confirm_password } = values;

    resetPassword(
      {
        countryCode: selectedValue,
        phone,
        code,
        password: new_password,
        passwordConfirmation: confirm_password,
      },
      {
        onSuccess: (res) => {
          if (res.code == 1) {
            Toast.show({
              content: res.message || "重置密码失败，请重试",
            });
          } else {
            Toast.show({
              content: "密码重置成功",
              afterClose: () => {
                navigate("/login");
              },
            });
          }
        },
        onError: (error) => {
          console.log(error);
          Toast.show({
            content: error.message || "重置密码失败，请重试",
          });
        },
      }
    );
  };

  const handleDropdownValue = (values: string) => {
    setSelectedValue(values);
  };

  const validatePhone = (phone: string): boolean => {
    // Basic phone validation - can be enhanced based on country code
    return /^\d{6,}$/.test(phone);
  };

  const handleGetVerificationCode = async (
    e: React.MouseEvent<HTMLButtonElement>
  ) => {
    e.preventDefault();
    const phone = form.getFieldValue("phone");

    if (!phone) {
      Toast.show({
        content: "请输入手机号码",
      });
      return;
    }

    if (!validatePhone(phone)) {
      Toast.show({
        content: "请输入有效的手机号码",
      });
      return;
    }

    await new Promise<void>((resolve) => {
      Modal.confirm({
        title: "消息提示",
        content: (
          <>
            <div className="my-0 mx-auto text-center w-[80%]">
              <p className="text-disabledIconGrey text-sm">
                是否向此手机号发送验证码?
              </p>
            </div>
          </>
        ),
        confirmText: "确定",
        bodyClassName: "lg:w-[80%] my-0 mx-auto confirmModalClassName",
        onConfirm: () => {
          sendResetCode(
            {
              countryCode: selectedValue,
              phone,
            },
            {
              onSuccess: (res) => {
                if (res.code == 1) {
                  Toast.show({
                    content: res.message || "发送验证码失败，请重试",
                  });
                  resolve();
                } else {
                  Toast.show({
                    content: "验证码已发送",
                  });
                  startCooldown();
                  resolve();
                }
              },
              onError: (error) => {
                Toast.show({
                  content: error.message || "发送验证码失败，请重试",
                });
                resolve();
              },
            }
          );
        },
      });
    });
  };

  return (
    <div className="bg-white h-dvh relative">
      <Navbar title="忘记密码" />
      <HeroHeader />
      <div className="bg-white">
        <Form
          form={form}
          layout="horizontal"
          className="p-7 loginForm"
          onFinish={handleFormOnFinish}
        >
          <div className="flex items-center gap-2 border-b border-gradientColorPrimary1 px-2 my-2">
            <img
              className="w-4 h-4 object-contain"
              src="/assets/images/user.png"
              alt="user"
            />
            <CountryCodeDropdown
              onChange={handleDropdownValue}
              defaultValue={selectedValue}
            />
            <Form.Item
              name="phone"
              rules={[
                { required: true, message: "请输入手机号码" },
                {
                  validator: (_, value) =>
                    validatePhone(value)
                      ? Promise.resolve()
                      : Promise.reject(new Error("请输入有效的手机号码")),
                },
              ]}
              className="mb-2"
            >
              <Input
                placeholder="请输入手机号码"
                className="base-input-placeholder"
              />
            </Form.Item>
          </div>
          <div className="flex items-center gap-2 border-b border-gradientColorPrimary1 px-2 my-2">
            <img
              className="w-4 h-4 object-contain"
              src="/assets/images/verified.png"
              alt="verified"
            />
            <Form.Item
              name="code"
              rules={[{ required: true, message: "请输入验证码" }]}
              className="mb-2 flex verifiedInput"
            >
              <Input
                placeholder="请输入验证码"
                className="base-input-placeholder"
              />
            </Form.Item>
            <Button
              loading={isSendingCode}
              onClick={handleGetVerificationCode}
              fill="none"
              className="gradientBtn w-1/3"
              size="mini"
              disabled={cooldown > 0}
            >
              {isSendingCode
                ? "发送中..."
                : cooldown > 0
                ? `${cooldown}秒后重试`
                : "获取验证码"}
            </Button>
          </div>
          <div className="flex items-center gap-2 border-b border-gradientColorPrimary1 px-2 my-2">
            <img
              className="w-4 h-4 object-contain"
              src="/assets/images/lock.png"
              alt="lock"
            />
            <Form.Item
              name="new_password"
              rules={[
                { required: true, message: "输入新密码" },
                { min: 6, message: "密码长度不能小于6位" },
              ]}
              className="mb-2 w-full"
            >
              <Input
                type={newPasswordVisible ? "text" : "password"}
                placeholder="输入新密码"
                className="base-input-placeholder"
              />
            </Form.Item>
            <img
              className="w-4 h-4 object-contain cursor-pointer"
              src={
                newPasswordVisible
                  ? "/assets/images/show.png"
                  : "/assets/images/no-show.png"
              }
              alt="back"
              onClick={() => setNewPasswordVisible((prev) => !prev)}
            />
          </div>
          <div className="flex items-center gap-2 border-b border-gradientColorPrimary1 px-2 my-2">
            <img
              className="w-4 h-4 object-contain"
              src="/assets/images/lock.png"
              alt="lock"
            />
            <Form.Item
              name="confirm_password"
              rules={[
                { required: true, message: "请确认密码" },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue("new_password") === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error("两次输入的密码不一致"));
                  },
                }),
              ]}
              className="mb-2 w-full"
            >
              <Input
                type={confirmPasswordVisible ? "text" : "password"}
                placeholder="请确认密码"
                className="base-input-placeholder"
              />
            </Form.Item>
            <img
              className="w-4 h-4 object-contain cursor-pointer"
              src={
                confirmPasswordVisible
                  ? "/assets/images/show.png"
                  : "/assets/images/no-show.png"
              }
              alt="back"
              onClick={() => setConfirmPasswordVisible((prev) => !prev)}
            />
          </div>
          <div>
            <Button
              loading={isResetting}
              fill="solid"
              className="gradientBtn w-full"
              type="submit"
            >
              找回密码
            </Button>
          </div>
        </Form>
      </div>
    </div>
  );
};

export default ForgetPassword;
