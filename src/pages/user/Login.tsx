import { useState } from "react";
import { loginMethodType } from "../../utils/types/login";
import { Button, Form, Input, Toast } from "antd-mobile";
import { useNavigate, useLocation } from "react-router";
import Navbar from "../../components/Navbar";
import CountryCodeDropdown from "../../components/CountryCodeDropdown";
import HeroHeader from "../../components/HeroHeader";
import TermAndConditionModal from "../../components/TermAndConditionModal";
import { useLogin, usePhoneLogin, useUserInfo } from "../../hooks/useAuth";
import { useUser } from "../../contexts/UserContext";
import { ThemeColor } from "@/components/ThemeColor";
// import styles from "./styles/login.module.css";
const loginOptions = [
  {
    id: 1,
    name: "用户登录",
    value: "user",
  },
  {
    id: 2,
    name: "手机登录",
    value: "phone",
  },
];

const Login = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { setUser } = useUser();
  const [termConditionsModalVisible, setTermConditionsModalVisible] =
    useState(false);
  const [loginMethod, setLoginMethod] = useState("user");
  const [selectedValue, setSelectedValue] = useState("+86");

  const loginMutation = useLogin();
  const phoneLoginMutation = usePhoneLogin();
  const { refetch: fetchUserInfo } = useUserInfo(false);

  const handleFormOnFinish = async (values: any) => {
    try {
      let response;

      if (loginMethod === "user") {
        response = await loginMutation.mutateAsync({
          username: values.username,
          password: values.password,
        });
      } else {
        response = await phoneLoginMutation.mutateAsync({
          phone: values.phone,
          countryCode: selectedValue,
          password: values.password,
        });
      }

      if (response.access_token) {
        // Store the access token
        localStorage.setItem("access_token", response.access_token);

        // Fetch user info after successful login
        const userInfoResponse = await fetchUserInfo();
        if (userInfoResponse.data) {
          setUser(userInfoResponse.data);
        }

        Toast.show({
          icon: "success",
          content: "登录成功",
        });

        // Redirect to the originally requested page or home page
        const from = location.state?.from?.pathname || "/";
        navigate(from, { replace: true });
      } else {
        Toast.show({
          icon: "fail",
          content: response.message || "登录失败，请重试",
        });
      }
    } catch (error) {
      console.log("error", error);
      Toast.show({
        icon: "fail",
        content: "登录失败，请重试",
      });
    }
  };

  const handleDropdownValue = (values: string) => {
    setSelectedValue(values);
  };

  return (
    <>
      <div className="bg-white h-dvh relative">
        {/* Navbar */}
        <ThemeColor color="primary" />
        <Navbar title="用户登陆" />
        <div className="pt-12">
          <HeroHeader />
          {/* Body */}
          <div className="">
            {/* Login Button - Option */}
            <div className="flex text-center h-[45px]">
              {loginOptions?.map((item: loginMethodType) => {
                return (
                  <button
                    key={item?.id}
                    className={`flex-1 text-base ${
                      loginMethod === item?.value
                        ? "bg-iconColorPrimary text-white"
                        : "bg-unselectedButtonColor text-textColorPrimary"
                    }`}
                    onClick={() => setLoginMethod(item?.value)}
                  >
                    {item?.name}
                  </button>
                );
              })}
            </div>
            {/* Login Form */}
            <div className="bg-white">
              <Form
                layout="horizontal"
                className="p-7 loginForm"
                onFinish={handleFormOnFinish}
              >
                {loginMethod === "user" ? (
                  <div className="flex items-center gap-2 border-b border-gradientColorPrimary1 px-2 my-2">
                    <img
                      className="w-4 h-4 object-contain"
                      src="/assets/images/user.png"
                      alt="user"
                    />
                    <Form.Item
                      name="username"
                      rules={[{ required: true, message: "请输入用户名" }]}
                      className="mb-2"
                    >
                      <Input
                        placeholder="请输入用户名"
                        className="base-input-placeholder"
                      />
                    </Form.Item>
                  </div>
                ) : (
                  <div className="flex items-center gap-2 border-b border-gradientColorPrimary1 px-2 my-2">
                    <img
                      className="w-4 h-4 object-contain"
                      src="/assets/images/user.png"
                      alt="user"
                    />
                    <CountryCodeDropdown
                      onChange={handleDropdownValue}
                      defaultValue={selectedValue}
                    />
                    <Form.Item
                      name="phone"
                      rules={[{ required: true, message: "请输入手机号码" }]}
                      className="mb-2"
                    >
                      <Input
                        placeholder="请输入正确手机号码"
                        className="base-input-placeholder"
                      />
                    </Form.Item>
                  </div>
                )}
                <div className="flex items-center gap-2 border-b border-gradientColorPrimary1 px-2 my-2">
                  <img
                    className="w-4 h-4 object-contain"
                    src="/assets/images/lock.png"
                    alt="lock"
                  />
                  <Form.Item
                    name="password"
                    rules={[{ required: true, message: "请输入密码" }]}
                    className="mb-2"
                  >
                    <Input
                      type="password"
                      placeholder="请输入密码"
                      className="base-input-placeholder"
                    />
                  </Form.Item>
                </div>
                <Form.Item className="text-right">
                  <Button
                    fill="none"
                    className="forgotPassword"
                    type="button"
                    onClick={() => navigate("/forget-password")}
                  >
                    忘记密码/登录异常
                  </Button>
                </Form.Item>
                <Form.Item>
                  <Button
                    fill="solid"
                    className="gradientBtn w-full"
                    type="submit"
                    loading={
                      loginMethod === "user"
                        ? loginMutation.isPending
                        : phoneLoginMutation.isPending
                    }
                  >
                    登录
                  </Button>
                </Form.Item>
              </Form>
              <div className="flex justify-center gap-2">
                <p className="text-disabledGrey text-base">没有账号?</p>
                <p
                  className="text-textColorPrimary text-base underline"
                  onClick={() => navigate("/register")}
                >
                  点击注册
                </p>
              </div>
            </div>
          </div>
          <div className="absolute bottom-3 w-full">
            <div className="text-center flex items-center justify-center gap-2">
              <input type="checkbox" />
              <p className="text-disabledIconGrey">
                我已阅读同意
                <span
                  className="text-textColorPrimary underline"
                  onClick={() => setTermConditionsModalVisible(true)}
                >
                  隐私条款
                </span>
              </p>
            </div>
          </div>
        </div>
      </div>
      <TermAndConditionModal
        visible={termConditionsModalVisible}
        setVisible={setTermConditionsModalVisible}
      />
    </>
  );
};

export default Login;
