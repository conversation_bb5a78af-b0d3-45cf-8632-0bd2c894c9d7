import Navbar from "../../components/Navbar";

const ExchangeGoods = () => {
  return (
    <div className="h-dvh">
      <Navbar
        title="入门型福利"
        addOnAfter={
          <img
            src="/assets/images/bubble.png"
            alt="chat"
            className="w-[22px] absolute top-1/2 right-3 -translate-y-1/2"
          />
        }
      />
      {/* 可兑换积分 */}
      <div className="bg-white">
        <div className="flex items-center justify-end gap-8 py-3 px-2 text-iconColorPrimary">
          <p>可兑换积分:</p>
          <p>0 积分</p>
        </div>
      </div>
      {/* 列表 */}
      <div className="grid grid-cols-2 gap-4 p-4">
        {/* Item */}
        <div>
          <img
            src="/assets/images/redeem-goods-item.png"
            alt="chat"
            className="w-full"
          />
          <div className="bg-white px-3 py-2 text-disabledIconGrey rounded-b-lg">
            <p>蕾丝连体开档款</p>
            <div className="flex items-center gap-3">
              <p className="text-iconColorPrimary">
                <span className="text-lg">200</span> 积分
              </p>
              <p>已售100+</p>
            </div>
            <button className="w-full bg-iconColorPrimary py-2 px-1 rounded-lg shadow-3xl text-white my-1">
              立即兑换
            </button>
          </div>
        </div>
        <div>
          <img
            src="/assets/images/redeem-goods-item.png"
            alt="chat"
            className="w-full"
          />
          <div className="bg-white px-3 py-2 text-disabledIconGrey rounded-b-lg">
            <p>蕾丝连体开档款</p>
            <div className="flex items-center gap-3">
              <p className="text-iconColorPrimary">
                <span className="text-lg">200</span> 积分
              </p>
              <p>已售100+</p>
            </div>
            <button className="w-full bg-iconColorPrimary py-2 px-1 rounded-lg shadow-3xl text-white my-1">
              立即兑换
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExchangeGoods;
