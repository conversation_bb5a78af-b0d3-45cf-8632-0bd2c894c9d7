import { useOrdersList } from "@/hooks/useApp";
import Empty from "../../components/Empty";
import Navbar from "../../components/Navbar";
import { useMemo } from "react";
import { AppOrder } from "@/types/api";
import dayjs from "dayjs";

const PurchaseFlow = () => {
  const { data, isLoading } = useOrdersList();
  console.log(data);
  const ordersData = useMemo(() => {
    if (!data?.application_orders || data.application_orders.length === 0) {
      return [];
    }

    // Map to create a dictionary of orders grouped by year/month
    const ordersByMonth = data.application_orders.reduce<
      Record<string, AppOrder[]>
    >((acc, order) => {
      // Convert timestamp to Date using dayjs.unix()
      const orderDate = dayjs.unix(order.created_at);
      const yearMonth = `${orderDate.year()}年${String(
        orderDate.month() + 1
      ).padStart(2, "0")}月`;

      if (!acc[yearMonth]) {
        acc[yearMonth] = [];
      }

      acc[yearMonth].push(order);
      return acc;
    }, {});

    // Convert the dictionary to array format needed for display
    return Object.entries(ordersByMonth).map(([yearMonth, orders], index) => ({
      id: index + 1,
      yearMonth,
      children: orders.map((order, childIndex) => {
        const orderDate = dayjs.unix(order.created_at);
        const date = `${orderDate.year()}.${String(
          orderDate.month() + 1
        ).padStart(2, "0")}.${String(orderDate.date()).padStart(2, "0")}`;

        // The name would typically come from a product lookup, this is a placeholder
        const name = order.product_name;

        return {
          id: childIndex + 1,
          date,
          name,
          price: `${order.amount}元`,
          orderNo: order.order_no,
          desc: order.remark,
        };
      }),
    }));
  }, [data]);

  return (
    <div className="h-dvh">
      <Navbar title="购买流水" />
      <div className="pt-12">
        {isLoading ? (
          <div className="flex justify-center items-center h-[80vh]">
            <p>加载中...</p>
          </div>
        ) : ordersData?.length > 0 ? (
          <>
            {ordersData?.map((item) => {
              return (
                <div key={item?.id}>
                  {/* DateTime */}
                  <p className="text-disabledIconGrey px-6 my-3">
                    {item?.yearMonth}
                  </p>
                  <div className="border-t-[1px] border-b-[1px] last:border-b-0">
                    {item?.children?.map((itemChild) => {
                      return (
                        <div
                          className="border-b-[1px] bg-white px-6"
                          key={itemChild?.id}
                        >
                          <div className="flex justify-between py-[2px] text-disabledIconGrey font-light">
                            <p>{itemChild?.date}</p>
                            <p>{itemChild?.name}</p>
                            <p>{itemChild?.price}</p>
                          </div>
                          <div>
                            <p className="text-textColorPrimary py-[2px]">
                              订单编号：{itemChild?.orderNo}
                            </p>
                          </div>
                          {itemChild?.desc && (
                            <div>
                              <p className="text-disabledIconGrey py-[2px]">
                                {itemChild?.desc}
                              </p>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </>
        ) : (
          <Empty />
        )}
      </div>
    </div>
  );
};

export default PurchaseFlow;
