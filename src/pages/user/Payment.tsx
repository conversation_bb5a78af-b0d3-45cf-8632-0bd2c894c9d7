import { useParams, useNavigate, useSearchParams } from "react-router";
import Navbar from "../../components/Navbar";
import { useState, useEffect } from "react";
import { useCreateOrder, usePaymentGatewayList } from "@/hooks/useApp";
import { PaymentChannel } from "@/types/api";
import { Toast } from "antd-mobile";
import { CheckOutline } from "antd-mobile-icons";
import { useGlobalAppConfig } from "@/hooks/useGlobalAppConfig";

const Payment = () => {
  const { payment_id = "" } = useParams();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { mutateAsync: createOrder, isPending } = useCreateOrder();
  const { data: paymentGatewaysData, isLoading } = usePaymentGatewayList();
  const { getPrimaryThemeHexColor } = useGlobalAppConfig();

  // Get price from URL search params
  const price = searchParams.get("price");

  const [selectedPayment, setSelectedPayment] = useState<number | null>(null);
  const [selectedChannel, setSelectedChannel] = useState<number | null>(null);
  const [paymentChannels, setPaymentChannels] = useState<PaymentChannel[]>([]);

  // Check if this is a topic payment
  const isTopic = searchParams.get("isTopic") === "true";

  // When payment gateways are loaded, set initial values
  useEffect(() => {
    if (paymentGatewaysData?.payment_gateways?.length) {
      const firstGateway = paymentGatewaysData.payment_gateways[0];
      setSelectedPayment(firstGateway.id);

      if (firstGateway.list?.length) {
        setPaymentChannels(firstGateway.list);
        setSelectedChannel(firstGateway.list[0].id);
      }
    }
  }, [paymentGatewaysData]);

  // Update channels when payment method changes
  const handlePaymentMethodChange = (gatewayId: number) => {
    setSelectedPayment(gatewayId);
    const selectedGateway = paymentGatewaysData?.payment_gateways.find(
      (gateway) => gateway.id === gatewayId
    );

    if (selectedGateway?.list?.length) {
      setPaymentChannels(selectedGateway.list);
      setSelectedChannel(selectedGateway.list[0].id);
    } else {
      setPaymentChannels([]);
      setSelectedChannel(null);
    }
  };

  const handleCreateOrder = async () => {
    if (!selectedPayment || !selectedChannel) return;

    try {
      const response = await createOrder({
        product_id: Number(payment_id),
        payment_gateway_id: selectedPayment,
        type: isTopic ? 2 : 1, // 1=VIP, 2=主题
      });
      console.log("Order created successfully:", response);

      // Redirect to payment_url if available
      if (response?.payment_url) {
        window.open(response.payment_url, "_blank");
      } else {
        Toast.show({
          content: response.message || "支付失败",
          icon: "fail",
        });
      }
    } catch (error) {
      console.error("Failed to create order:", error);
    }
  };

  return (
    <div className="h-dvh">
      <Navbar title="支付方式" />
      <div className="pt-12">
        <div className="px-3 py-5 text-disabledIconGrey">
          {/* 支付金额 */}
          <div className="border-b py-1">
            <div className="flex items-center justify-between ">
              <p>支付金额</p>
              <div className="flex items-center gap-4">
                <p>当前支付金额</p>
                <p className="text-black text-lg text-medium">
                  ¥ {price ? parseFloat(price).toFixed(2) : "0.00"}元
                </p>
              </div>
            </div>
          </div>

          {/* 支付方式 */}
          <div className="border-b py-3">
            <p>支付方式</p>
            {isLoading ? (
              <p>加载中...</p>
            ) : (
              <div className="flex items-center gap-3">
                {paymentGatewaysData?.payment_gateways?.map((gateway) => (
                  <div
                    key={gateway.id}
                    onClick={() => handlePaymentMethodChange(gateway.id)}
                  >
                    <div className="relative w-[80px] border border-disabledIconGrey/50 rounded-lg px-[10px] py-1 my-1">
                      <img
                        className="w-full"
                        src={gateway.icon || "/assets/images/alipay.png"}
                        alt={gateway.name}
                      />
                      <div>
                        <p className="text-center">{gateway.name}</p>
                        <div className="w-[18px] h-[18px] absolute -bottom-[1px] -right-[1px] border border-disabledIconGrey/50 rounded-full flex items-center justify-center">
                          {selectedPayment === gateway.id && (
                            <CheckOutline
                              style={{ color: getPrimaryThemeHexColor() }}
                              fontSize={14}
                            />
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 支付渠道 */}
          <div className="border-b py-3">
            <p>支付渠道</p>
            <div className="flex items-center gap-3 py-1">
              {paymentChannels.map((channel) => (
                <div
                  key={channel.id}
                  onClick={() => setSelectedChannel(channel.id)}
                  className="relative border border-disabledIconGrey/50 rounded-lg px-4 py-2"
                >
                  <p>{channel.name}</p>
                  {channel.icon && (
                    <img
                      src={channel.icon}
                      alt={channel.name}
                      className="w-[60px] mt-1"
                    />
                  )}
                  <div className="w-[18px] h-[18px] absolute -bottom-[1px] -right-[1px] border border-disabledIconGrey/50 rounded-full flex items-center justify-center">
                    {selectedChannel === channel.id && (
                      <CheckOutline
                        style={{ color: getPrimaryThemeHexColor() }}
                        fontSize={14}
                      />
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 注意事项 */}
          <div className="py-3 pb-16">
            <div className="py-1">
              <p className="py-2">注意事项：</p>
              <p className="text-textColorPrimary">
                付款成功之后请退出账户，重新登录即可获得猫咪VIP。
              </p>
            </div>
            <div>
              <p className="py-2">数字货币事项</p>
              <p className="py-1">
                虚拟币购买安全，便捷。用户可以通过数字货币交易平台来购买USDT(虚拟货币)，在猫咪进行购买VIP。
              </p>
              <p className="py-1">购买USDT数字货币详细教程(USDT购买的优势)</p>
              <p className="py-1">
                泰达币（Tether）也被称为USDT，其价值等同于美元。1 USDT≈1
                美元，该特点使USDT成为全球加密数字货币中规模最大、流通性最高的稳定币，具有良好的保值性，也是网站所使用的主要币种。
              </p>
              <div className="py-1">
                <p>USDT高度加密</p>
                <p>
                  与银行卡等传统交易方式相比，用户不需要给出自己的姓名或卡号即可完成虚拟币交易，避免敏感信息泄露。
                </p>
              </div>
              <div className="py-1">
                <p>USDT去中心化</p>
                <p>
                  不由央行或当局发行，不受银行监管，用户可随心所欲地使用存放在自己数字钱包里的资金
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Payment button */}
      <div className="fixed bottom-0 w-full p-4 bg-white">
        <button
          onClick={handleCreateOrder}
          disabled={isPending || !selectedPayment || !selectedChannel}
          className="w-full py-3 bg-textColorPrimary text-white rounded-lg disabled:bg-gray-400"
        >
          {isPending ? "处理中..." : "确认支付"}
        </button>
      </div>
    </div>
  );
};

export default Payment;
