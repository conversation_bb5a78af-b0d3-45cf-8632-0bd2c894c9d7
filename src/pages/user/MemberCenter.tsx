import { useNavigate } from "react-router";
import Navbar from "../../components/Navbar";
import { useProductsList } from "@/hooks/useContent";
import { Product } from "@/types/api";

const MemberCenter = () => {
  const navigate = useNavigate();
  const { data } = useProductsList();
  const productsList = data?.app_products || [];
  console.log(productsList);
  return (
    <div className="h-dvh">
      <Navbar title="会员中心" />
      <div className="px-3 py-4 pt-16">
        {productsList.map((item: Product) => {
          return (
            <div
              key={item.id}
              className="relative mb-3"
              onClick={() =>
                navigate(`/me/payment/${item.id}?price=${item.price}`)
              }
            >
              {item.hot === 1 && (
                <div
                  className="absolute top-0 left-0 z-10 px-2 py-1 text-white text-xs font-medium rounded-e-lg rounded-tl-lg"
                  style={{
                    background:
                      "linear-gradient(90deg, #FD9657 0%, #FF0000 100%)",
                  }}
                >
                  限时热销
                </div>
              )}
              <div>
                <img className="w-full" src="/assets/images/vip-card.png" />
              </div>
              <div className="absolute top-1/2 left-5 -translate-y-1/2">
                <p className="text-white text-[20px] font-medium mb-3">
                  {item.name}
                </p>
                <div className="text-textColorPrimaryDark mb-3 font-medium">
                  <p>{item.description || ""}</p>
                </div>
                <div className="flex items-center gap-4">
                  <p className="text-lg text-textColorPrimaryDark ">
                    {item.price}元
                  </p>
                  {item.original_price && (
                    <p className="text-xs text-textColorPrimary line-through">
                      原价{item.original_price}元
                    </p>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default MemberCenter;
