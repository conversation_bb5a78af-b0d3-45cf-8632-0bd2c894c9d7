import { useNavigate } from "react-router";
import Navbar from "../../components/Navbar";

const items = [
  {
    id: 1,
    name: "高端型福利",
    img: "/assets/images/vip-gold.png",
    points: "10000",
  },
  {
    id: 2,
    name: "高级型福利",
    img: "/assets/images/vip-diamond.png",
    points: "5000",
  },
  {
    id: 3,
    name: "中级型福利",
    img: "/assets/images/vip-platinum.png",
    points: "1000",
  },
  {
    id: 4,
    name: "入门型福利",
    img: "/assets/images/vip-silver.png",
    points: "500",
  },
];

const PointsRedemption = () => {
  const navigate = useNavigate();
  return (
    <div className="h-dvh">
      <Navbar
        title="积分兑换"
        addOnAfter={
          <img
            src="/assets/images/bubble.png"
            alt="chat"
            className="w-[22px] absolute top-1/2 right-3 -translate-y-1/2"
          />
        }
      />
      {/* 可兑换积分 列表 */}
      <div className="my-5 mx-3 border">
        {/* Header */}
        <div className="flex items-center gap-3 py-4 px-7 bg-iconColorPrimary text-white text-base">
          <p>可兑换积分:</p>
          <p>0 积分</p>
        </div>
        {/* 列表 - content */}
        <div className="bg-white">
          <div className="flex items-center text-center py-1 border-b py-2">
            <p className="flex-1">福利名称</p>
            <p className="flex-1">积分要求</p>
            <p className="flex-1">入口</p>
          </div>
          {items?.map((item) => {
            return (
              <div
                className="flex items-center text-center py-2 border-b"
                key={item?.id}
              >
                <div className="text-center flex-1">
                  <img
                    className="w-[60px] h-[50px] object-contain cursor-pointer my-0 mx-auto"
                    src={item?.img}
                    alt="vip-gold"
                  />
                  <p>{item?.name}</p>
                </div>
                <div className="flex-1">
                  <p className="text-sm">{item?.points}积分</p>
                </div>
                <div className="flex-1">
                  <button
                    className="bg-iconColorPrimary py-2 px-6 rounded-lg shadow-3xl text-white"
                    onClick={() => {
                      navigate("/me/exchange-goods");
                    }}
                  >
                    查看商品
                  </button>
                </div>
              </div>
            );
          })}
        </div>
      </div>
      {/* 兑换规则 */}
      <div className="bg-white my-5 mx-3 border">
        <div className="px-6 py-2">
          <p className="font-semibold">兑换规则:</p>
          <ul className="font-semibold">
            <li className="my-2">
              <p>1兑换成功后请联系在线客服提供有效的邮寄地址；</p>
            </li>
            <li className="my-2">
              <p>
                2点击兑换成功后，有效期为3天，若超过3天则视为放弃礼品，平台不予赔偿
              </p>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default PointsRedemption;
