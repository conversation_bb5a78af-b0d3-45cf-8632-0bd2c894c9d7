import { NavLink } from "react-router";
import Navbar from "../../components/Navbar";

const Setting = () => {
  return (
    <div className="h-lvh">
      <Navbar title="设置" />
      <div className="pt-12">
        <div className=" p-5">
          {" "}
          <div className="bg-white p-4">
            <div className="border-b border-disabledGrey/50 pb-3 pt-1 mb-3">
              <NavLink
                to="/me/change-password"
                className="flex items-center justify-between"
              >
                <div className="flex items-center gap-2 pl-2">
                  <img
                    src="/assets/images/password-lock.png"
                    alt="agent"
                    width={18}
                  />
                  <p className="text-[#333]">修改密码</p>
                </div>
                <div className="pr-2">
                  <p className="text-[11px] text-disabledIconGrey">
                    修改新的密码
                  </p>
                </div>
              </NavLink>
            </div>
            <div className="pt-1">
              <NavLink
                to="/me/bind-phone"
                className="flex items-center justify-between"
              >
                <div className="flex items-center gap-2 pl-2">
                  <img
                    src="/assets/images/bind-phone.png"
                    alt="agent"
                    width={18}
                  />
                  <p className="text-[#333]">手机绑定</p>
                </div>
                <div className="pr-2">
                  <p className="text-[11px] text-disabledIconGrey">
                    绑定手机号码
                  </p>
                </div>
              </NavLink>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Setting;
