import { Ta<PERSON>, Swiper } from "antd-mobile";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router";
import { motion } from "framer-motion";
import {
  MenuItemChild,
  MenusResponse,
  MenuItem,
  BenefitItem,
} from "../../types/api"; // Adjust path if needed
import { useGlobalMenus } from "@/contexts/MenusContext"; // Adjust path if needed
import Navbar from "@/components/Navbar"; // Adjust path if needed

// Removed ICategoryPopup interface as props are no longer passed this way

const CategoryPage = () => {
  // Keep state and navigation hooks
  const [selectedId, setSelectedId] = useState<Record<number, number>>({});
  const navigate = useNavigate();
  const { data: menuData } = useGlobalMenus(); // Get data from context

  // Keep useEffect to initialize selected IDs
  useEffect(() => {
    if (!menuData?.appMenus) return;

    const ids: Record<number, number> = {};

    // Structure:
    // Normal menus (parent_id = 0)
    if (menuData.appMenus.normal && menuData.appMenus.normal.length > 0) {
      ids[1] = menuData.appMenus.normal[0].id;
    }

    // VIP menus (parent_id = 0)
    if (menuData.appMenus.vip && menuData.appMenus.vip.length > 0) {
      ids[2] = menuData.appMenus.vip[0].id;
    }

    // Benefits menus
    if (menuData.appMenus.benefits && menuData.appMenus.benefits.length > 0) {
      ids[4] = menuData.appMenus.benefits[0].id;
    }

    // Topics menus
    if (menuData.appMenus.topic && menuData.appMenus.topic.length > 0) {
      ids[3] = menuData.appMenus.topic[0].id;
    }

    setSelectedId(ids);
  }, [menuData]);

  // Keep helper functions, replace onClose with navigate(-1)
  const onChangeCategoryMenu = (parentId: number, id: number) => {
    setSelectedId((prev) => {
      return { ...prev, [parentId]: id };
    });
  };

  const handleCategoryClick = (id: number) => {
    navigate(`/category-list/${id}`);
    // onClose(); // Removed
  };

  const handleTopicClick = (id: number) => {
    navigate(`/theme/detail/${id}`);
    // onClose(); // Removed
  };

  if (!menuData?.appMenus) {
    // Can show loading state or return null
    return null; // Or a loading indicator
  }

  // Keep data transformation logic
  const categoryList = [
    {
      id: 1,
      title: "片库",
      children: menuData.appMenus.normal || [],
      type: "normal",
    },
    {
      id: 2,
      title: "VIP专区",
      children: menuData.appMenus.vip || [],
      type: "normal",
    },
    {
      id: 3,
      title: "限时专题",
      children: menuData.appMenus.topic || [],
      type: "topics",
    },
    {
      id: 4,
      title: "福利",
      children: menuData.appMenus.benefits || [],
      type: "benefits",
    },
  ];

  return (
    // Replace Popup with standard page structure
    <motion.div
      className="h-dvh flex flex-col bg-white"
      initial={{ x: "100%" }}
      animate={{ x: 0 }}
      exit={{ x: "100%" }}
      transition={{ type: "tween", ease: "easeOut", duration: 0.3 }}
    >
      <Navbar title="分类" />
      {/* Keep the main content rendering logic */}
      <div className="flex-grow overflow-y-auto pt-12">
        {/* Removed Drawer Title - handled by Navbar */}
        {/* Drawer Category */}
        <div>
          {categoryList.map((category) => {
            const children1 = category?.children;
            const children1Id = selectedId[category?.id];

            // Handle different types based on the category type
            const children2 =
              category.type === "normal"
                ? (children1 as MenuItem[])?.find(
                    (item) => item?.id === children1Id
                  )?.child
                : undefined;

            const selectedBenefitParent =
              category.type === "benefits" && children1Id
                ? (children1 as BenefitItem[])?.find(
                    (item) => item?.id === children1Id
                  )
                : undefined;

            if (children1 && children1?.length > 0) {
              return (
                <div className="mb-8" key={category?.id}>
                  <div
                    className={`${
                      category.id === 2
                        ? "bg-iconColorGold"
                        : "bg-iconColorPrimary"
                    }`}
                  >
                    <h3 className="text-center text-base text-white py-1">
                      {" "}
                      {/* Added padding */}
                      {category?.title}
                    </h3>
                  </div>
                  {category.type === "normal" ? (
                    <>
                      <div className="flex items-center gap-3 px-2 py-1 text-disabledIconGrey overflow-x-auto">
                        {" "}
                        {/* Added overflow */}
                        {children1Id &&
                          (children1 as MenuItem[])?.map((item) => {
                            return (
                              <div
                                className="relative flex-shrink-0"
                                key={item?.id}
                              >
                                {" "}
                                {/* Added shrink */}
                                <p
                                  className={`${
                                    children1Id === item?.id
                                      ? `text-sm relative after:absolute after:content-[''] after:h-[2px] after:mx-auto after:left-0 after:right-0 after:bottom-[-3px] after:w-[85%] after:rounded-[50px] ${
                                          category.id === 2
                                            ? "text-iconColorGold after:bg-iconColorGold"
                                            : "text-iconColorPrimary after:bg-iconColorPrimary"
                                        }`
                                      : "text-sm"
                                  }`}
                                  onClick={() => {
                                    onChangeCategoryMenu(
                                      category?.id,
                                      item?.id
                                    );
                                  }}
                                >
                                  {item?.title}
                                </p>
                              </div>
                            );
                          })}
                      </div>
                      <div className="grid grid-cols-3 gap-4 px-3 py-2 text-disabledIconGrey">
                        {children2?.map((item: MenuItemChild) => {
                          return (
                            <p
                              key={item?.id}
                              className={`text-sm text-center ${
                                category.id === 2
                                  ? "bg-iconColorGold/10 border border-iconColorGold"
                                  : "bg-iconColorPrimary/10 border border-iconColorPrimary"
                              } px-[16px] py-[6px] rounded-[10px] cursor-pointer`}
                              onClick={() => handleCategoryClick(item.id)}
                            >
                              {item?.title}
                            </p>
                          );
                        })}
                      </div>
                    </>
                  ) : category.type === "benefits" ? (
                    <>
                      <div className="flex items-center gap-3 px-2 py-1 text-disabledIconGrey overflow-x-auto">
                        {" "}
                        {/* Added overflow */}
                        {(children1 as BenefitItem[])?.map((item) => {
                          return (
                            <div
                              className="relative flex-shrink-0"
                              key={item?.id}
                            >
                              {" "}
                              {/* Added shrink */}
                              <p
                                className={`${
                                  children1Id === item?.id
                                    ? "text-sm relative after:absolute after:content-[''] after:h-[2px] after:mx-auto after:left-0 after:right-0 after:bottom-[-3px] after:w-[85%] after:rounded-[50px] text-iconColorPrimary after:bg-iconColorPrimary"
                                    : "text-sm"
                                }`}
                                onClick={() =>
                                  onChangeCategoryMenu(category?.id, item?.id)
                                }
                              >
                                {item?.title}
                              </p>
                            </div>
                          );
                        })}
                      </div>
                      <div className="flex overflow-x-auto flex-nowrap gap-4 px-3 py-2 text-disabledIconGrey">
                        <Swiper
                          indicator={() => null}
                          slideSize={23}
                          stuckAtBoundary={false}
                          style={
                            {
                              "--slide-gap": "8px",
                            } as React.CSSProperties
                          }
                          className="px-3 py-2"
                        >
                          {selectedBenefitParent?.child &&
                          selectedBenefitParent.child.length > 0
                            ? selectedBenefitParent.child.map((childItem) => (
                                <Swiper.Item key={childItem.id}>
                                  <div
                                    className="text-center cursor-pointer w-16"
                                    onClick={() =>
                                      window.open(childItem.url, "_blank")
                                    }
                                  >
                                    <img
                                      src={childItem.img}
                                      alt={childItem.title}
                                      className="w-full h-auto rounded-lg mb-1"
                                    />
                                    <p className="text-xs text-center whitespace-normal">
                                      {childItem?.title}
                                    </p>
                                  </div>
                                </Swiper.Item>
                              ))
                            : selectedBenefitParent && ( // Display parent if no children or parent itself
                                <Swiper.Item key={selectedBenefitParent.id}>
                                  {/* Wrap single item logic similar to child mapping */}
                                  <div
                                    className="text-center cursor-pointer w-16"
                                    onClick={() => {
                                      // Decide action: open URL if exists, else navigate
                                      if (selectedBenefitParent.url) {
                                        window.open(
                                          selectedBenefitParent.url,
                                          "_blank"
                                        );
                                      } else {
                                        // Assuming benefit items might also navigate like categories sometimes?
                                        // If not, remove this else block.
                                        // handleCategoryClick(selectedBenefitParent.id);
                                        console.warn(
                                          "Benefit item without children has no URL:",
                                          selectedBenefitParent.title
                                        );
                                      }
                                    }}
                                  >
                                    <img
                                      src={selectedBenefitParent.img}
                                      alt={selectedBenefitParent.title}
                                      className="w-full h-auto rounded-lg mb-1"
                                    />
                                    <p className="text-xs text-center whitespace-normal">
                                      {selectedBenefitParent?.title}
                                    </p>
                                  </div>
                                </Swiper.Item>
                              )}
                        </Swiper>
                      </div>
                    </>
                  ) : (
                    // For topics - simple flat display
                    <div className="grid grid-cols-3 gap-4 px-3 py-2 text-disabledIconGrey">
                      {(children1 as MenuItem[])?.map((item) => {
                        return (
                          <p
                            key={item?.id}
                            className="text-sm text-center bg-iconColorPrimary/10 border border-iconColorPrimary px-[16px] py-[6px] rounded-[10px] cursor-pointer"
                            onClick={() => handleTopicClick(item.id)}
                          >
                            {item?.title}
                          </p>
                        );
                      })}
                    </div>
                  )}
                </div>
              );
            }
            return null;
          })}
        </div>
      </div>
    </motion.div>
  );
};

export default CategoryPage;
