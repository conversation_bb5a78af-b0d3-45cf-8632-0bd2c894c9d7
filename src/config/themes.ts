/**
 * Theme configuration file
 * Defines color schemes for different primary themes
 */

export const primaryThemes = {
  green: {
    textColorPrimary: "87 183 138",
    textColorPrimaryDark: "83 156 132",
    iconColorPrimary: "87 183 138",
    gradientColorPrimary1: "74 176 135",
    gradientColorPrimary2: "177 232 161",
    themeHexColor: "#4AB087",
  },
  blue: {
    textColorPrimary: "90 197 239",
    textColorPrimaryDark: "60 145 212",
    iconColorPrimary: "90 197 239",
    gradientColorPrimary1: "85 167 224",
    gradientColorPrimary2: "95 228 255",
    themeHexColor: "#5AC5EF",
  },
  orange: {
    textColorPrimary: "248 152 30", // from #F8981E
    textColorPrimaryDark: "212 111 60", // from #D46F3C
    iconColorPrimary: "248 152 30", // from #F8981E
    // from linear-gradient(360deg, #FFB353 0%, #F8981E 100%)
    // Assuming gradientColorPrimary1 is the start color and gradientColorPrimary2 is the end color
    // For 360deg, the start is effectively the bottom, end is the top. Let's keep semantic order.
    gradientColorPrimary1: "248 152 30", // #F8981E (end color of 360deg gradient, effectively top)
    gradientColorPrimary2: "255 179 83", // #FFB353 (start color of 360deg gradient, effectively bottom)
    themeHexColor: "#F8981E",
  },
  black: {
    textColorPrimary: "25 26 31", // from #191A1F
    textColorPrimaryDark: "231 231 231", // from #E7E7E7 (This is a light color for dark text, unusual but as specified)
    iconColorPrimary: "25 26 31", // from #191A1F
    // from linear-gradient(180deg, #191A1F 0%, #2F3139 100%)
    gradientColorPrimary1: "25 26 31", // #191A1F (start color)
    gradientColorPrimary2: "47 49 57", // #2F3139 (end color)
    themeHexColor: "#191A1F",
  },
  pink: {
    textColorPrimary: "245 111 101", // from #F56F65
    textColorPrimaryDark: "231 231 231", // from #E7E7E7 (This is a light color for dark text, unusual but as specified)
    iconColorPrimary: "245 111 101", // from #F56F65
    // from linear-gradient(180deg, #F56F65 0%, #FFACA6 100%)
    gradientColorPrimary1: "245 111 101", // #F56F65 (start color)
    gradientColorPrimary2: "255 172 166", // #FFACA6 (end color)
    themeHexColor: "#F56F65",
  },
  // Add more primary themes as needed
};

export type PrimaryThemeKey = keyof typeof primaryThemes;
