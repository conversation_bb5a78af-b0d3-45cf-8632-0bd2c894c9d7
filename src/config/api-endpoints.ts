export const API_ENDPOINTS = {
  // Auth endpoints
  login: "/login",
  phoneLogin: "/phone-login",
  register: "/register",
  sendResetCode: "/send-reset-code",
  updatePassword: "/update-password",
  resetForgotPassword: "/reset-forgot-password",

  // User endpoints
  me: "/me",
  sendVerificationCode: "/send-verification-code",
  bindPhone: "/bind-phone",

  // Order endpoints

  appOrdersList: "/app-orders/list",
  appOrdersCreate: "/app-orders/create",
  appMemberTopicsList: "/app-member-topics/list",
  paymentGatewayList: "/payment-gateways",

  // Config endpoints
  config: "/config",
  appMenus: "/app-menus",
  banners: "/banners",
  notices: "/notices",
  scrollingMessages: "/scrolling-messages",
  appVersions: "/app-versions",

  // Video endpoints
  appVideosIndex: "/app-videos/index",
  appVideosList: "/app-videos/list",
  appVideosDetails: "/app-videos/details",
  appVideosRelated: "/app-videos/related",

  // Topic endpoints
  appTopicsList: "/app-topics/list",
  appTopicsDetails: "/app-topics/details",

  // Product endpoints
  appProductsList: "/app-products/list",

  // Import endpoints
  importVideo: "/import/video",
  importTopic: "/import/topic",
} as const;

// Type for API endpoints
export type ApiEndpoint = keyof typeof API_ENDPOINTS;

// App constant - Read from environment variable provided by Vite
export const APP_KEY = import.meta.env.VITE_APP_KEY || "l1ocvr2hrhp3jlcu"; // Fallback just in case
