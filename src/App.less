// Home
.adm-button-gold {
  background-color: rgb(var(--icon-color-gold)) !important;
}

.menuSelected::after {
  position: absolute;
  content: "";
  height: 2px;
  margin: 0 auto;
  left: 0;
  right: 0;
  bottom: 0;
  width: 50%;
  background: #fff;
  border-radius: 50px;
}

.bannerDesc {
  background: linear-gradient(
    180deg,
    rgb(var(--disabled-icon-grey) / 0) 0%,
    rgb(var(--text-color-primary) / 0.5) 100%
  );
}

// Header main menu
.mainMenuTabs {
  --active-line-color: #fff;
  --active-title-color: #fff;
  --active-line-height: 2px;
  .adm-tabs-tab-wrapper-stretch {
    flex: none;
  }

  .adm-tabs-header {
    border-bottom: none;
  }

  .adm-tabs-tab,
  .adm-tabs-tab-active {
    color: #fff;
    padding-bottom: 4px;
  }

  .adm-tabs-tab-active {
    position: relative;
  }

}

// Category Popup
.drawerPopup {
  top: 0;
  left: 50%;
  width: 100%;
  height: 100%;
  transform: translate(-50%, 0px);
  overflow-x: hidden;

  .adm-mask {
    position: absolute;
    top: 0;
    left: 0;
  }
  .adm-popup-body {
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
  }

  .adm-popup-close-icon {
    left: auto;
    right: 8px;
  }
}

.categoryMenuSelected::after {
  position: absolute;
  content: "";
  height: 2px;
  margin: 0 auto;
  left: 0;
  right: 0;
  bottom: -3px;
  width: 85%;
  background: rgb(var(--icon-color-primary));
  border-radius: 50px;
}

// Marquee Notice Bar
.noticeBar {
  --background-color: rgb(var(--icon-color-primary) / 0.5);
  --border-color: none;
  --height: 30px;
  padding: 0;

  .adm-notice-bar-content-inner {
    color: #000;
    font-size: 12px;
  }
}

// Tab Wrapper
.tabWrapper {
  .adm-tab-bar-item-active {
    color: rgb(var(--icon-color-primary));
  }
}

.vip-theme {
  --current-gradient-start: #F4E3C2;
  --current-gradient-end: #DEBD81;

  .tabWrapper {
    .adm-tab-bar-item-active {
      color: rgb(var(--text-color-gold));
    }
  }

  // Main menu tabs
  .mainMenuTabs {
    .adm-tabs-tab {
    }

    .adm-tabs-tab-active {
      font-size: 16px;
      font-weight: 500;
    }

    .adm-tabs-tab-line {
      height: 3px;
      border-radius: 2px;
    }
  }

  // Notice bar
  .noticeBar {
    --background-color: rgb(var(--icon-color-gold) / 0.5);
    
    &.vip-notice {
      --adm-color-primary: rgb(var(--text-color-gold));
      background: rgba(221, 188, 128, 0.1);
      border: 1px solid rgba(221, 188, 128, 0.2);

      .adm-notice-bar-content {
        color: rgb(var(--text-color-gold));
      }
    }
  }

  // Buttons
  .primayBtn {
    background-color: rgb(var(--text-color-gold)) !important;
    box-shadow: 0 2px 8px rgba(221, 188, 128, 0.25);
  }

  .primayIconBtn {
    background-color: rgb(var(--text-color-gold)) !important;
    box-shadow: 0 2px 8px rgba(221, 188, 128, 0.25);
  }

  // Text colors
  .text-textColorPrimary {
    color: rgb(var(--text-color-gold)) !important;
  }

  // Gradient backgrounds
  .gradient-primary {
    background: linear-gradient(to bottom, var(--current-gradient-start), var(--current-gradient-end)) !important;
  }
}

.meHeaderBg {
  // background: linear-gradient(
  //   178.39deg,
  //   rgb(var(--gradient-color-primary1)) 1.29%,
  //   rgb(var(--gradient-color-primary2)) 43.12%,
  //   rgb(var(--gradient-color-primary3)) 65.99%,
  //   rgb(var(--gradient-color-primary3) / 0) 98.58%
  // );
  background: linear-gradient(
    180deg,
    rgb(var(--gradient-color-primary1)) 0%,
    rgb(var(--gradient-color-primary2)) 100%
  );
}

// Placeholder
.sm-input-placeholder input::placeholder {
  font-size: 13px;
}

.base-input-placeholder input::placeholder {
  font-size: 14px;
}

// Form
.mmForm {
  --border-top: 0px;
  --border-bottom: 0px;
  --border-inner: 0px;

  .adm-list-body {
    overflow: unset;
    // overflow-x: hidden;
  }
  .adm-list-body-inner {
    padding: 0 20px;
  }
  .adm-list-item {
    padding-left: 0px;
    padding-right: 20px;
  }

  .adm-form-footer {
    margin-top: 15px;
  }

  // .adm-form-item-feedback-error {
  //   padding-left: 28px;
  // }
}

// Login Form
.loginForm {
  --border-top: 0px;
  --border-bottom: 0px;

  .adm-list-body {
    overflow: unset;
    // overflow-x: hidden;
  }
  .adm-list-item-content-main {
    padding: 10px 0px 0px 0px;
  }

  .adm-list-item-content {
    border-top: 0px;
    padding: 0px;
  }

  .forgotPassword span {
    color: rgb(var(--text-color-primary));
    text-decoration: underline;
    font-size: 14px;
  }
}

// Hint Modal
.hintModalClassName {
  border-radius: 14px;
  padding: 10px 0px;
  width: 80%;
  margin: 0 auto;

  @media only screen and (max-width: 600px) {
    width: 100%;
  }
  .adm-button {
    border: none;
    border-radius: 50px;
    background-color: rgb(var(--icon-color-primary));
  }
}

// Confirm Modal
.confirmModalClassName {
  border-radius: 14px;
  padding: 10px 0px;
  width: 100%;
  margin: 0 auto;

  .adm-space {
    flex-direction: row;
    align-items: center;
    --gap-vertical: 0px;
    gap: 16px;
  }

  .adm-space-item {
    flex: 1;
  }

  .adm-modal-button-primary {
    font-size: 14px;
    border: none;
    border-radius: 50px;
    background-color: rgb(var(--icon-color-primary));
  }

  .adm-button.adm-button-large {
    padding-top: 4px;
    padding-bottom: 4px;
  }

  .adm-button-fill-none {
    font-size: 14px;
    color: #fff;
    border: none;
    border-radius: 50px;
    background-color: rgb(var(--disabled-icon-grey));
  }
  .adm-modal-footer .adm-modal-button:not(.adm-modal-button-primary) {
    padding-top: 4px;
    padding-bottom: 4px;
  }
}

// dropdown Menu
.dropDownMenuBox {
  position: absolute;
  max-width: 180px;
  width: max-content;
  left: 0;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0px 3px 6px 0px rgba(0, 0, 0, 0.2);
  transition: all 0.3s;
  -webkit-transition: all 0.3s;
  -moz-transition: all 0.3s;
  -ms-transition: all 0.3s;
  -o-transition: all 0.3s;
  // visibility: hidden;
  // opacity: 0;
  margin-top: 5px;
}

.dropDownMenuBox:before {
  content: "";
  background-color: transparent;
  border-right: 8px solid transparent;
  position: absolute;
  border-left: 8px solid transparent;
  border-bottom: 8px solid #fff;
  border-top: 8px solid transparent;
  top: -15px;
  left: 40%;
  transform: translateX(-40%);
}

.dropDownMenuBox:after {
  content: "";
  background-color: transparent;
}

// 规则说明 Modal - customRulesModal
.customRulesModal {
  // .adm-center-popup-body {
  //   background-color: transparent;
  // }

  .adm-modal-body {
    overflow: unset;
  }

  .adm-modal-image-container {
    overflow-y: visible;
    background-color: rgb(var(--icon-color-primary));
  }
  .adm-image {
    overflow: unset;
  }

  .adm-image-img {
    width: 80px;
    margin: -50px auto 0 auto;
  }
  .adm-center-popup-wrap {
    max-width: 25vw;
    width: 25vw;
  }
  @media only screen and (max-width: 600px) {
    .adm-center-popup-wrap {
      max-width: 85vw;
      width: 85vw;
    }
  }

  .adm-space-item button {
    width: 100px;
    height: 32px;
    color: #fff;
    font-size: 14px;
    margin: 0 auto;
    background-color: rgb(var(--icon-color-primary));
    box-shadow: 0 4px 4px -0px rgba(0, 0, 0, 0.3);
  }

  // .adm-modal-title,
  // .adm-modal-content,
  // .adm-modal-footer {
  //   background: #fff;
  // }
}

// Bottom Pop up
.bottomPopup {
  position: absolute;
  bottom: -10px;
  left: 50%;
  width: 100%;
  height: 100%;
  transform: translateX(-50%);
  // transform: translate(-50%, 0px);
  overflow-x: hidden;
}

// Tabs - list
.listTabs {
  color: rgb(var(--disabled-icon-grey));
  --active-line-color: rgb(var(--icon-color-primary));
  --active-title-color: rgb(var(--icon-color-primary));
  .adm-tabs-tab-wrapper-stretch {
    flex: none;
  }
}

// 验证码 input
.verifiedInput {
  .adm-form-item-child-inner {
    display: flex;
  }
}

.downloadBundleModal {
  .adm-center-popup-wrap {
    max-width: 25vw;
    width: 25vw;
  }
  @media only screen and (max-width: 600px) {
    .adm-center-popup-wrap {
      max-width: 95vw;
      width: 95vw;
    }
  }
}

.customModal {
  .adm-center-popup-wrap {
    max-width: 28vw;
    width: 28vw;
  }
  @media only screen and (max-width: 600px) {
    .adm-center-popup-wrap {
      max-width: 95vw;
      width: 95vw;
    }
  }
}
// .dropDownMenuBoxActive {
//   visibility: visible !important;
//   opacity: 1 !important;
// }
// // Dropdown
// .dropdownPopup {
//   .adm-dropdown-popup {
//     position: absolute;
//     left: 50%;
//     // width: 100%;
//     // height: 100%;
//     transform: translate(-50%, 0px);
//     overflow-x: hidden;
//     width: 355px;
//   }

//   // .adm-mask {
//   //   opacity: 0 !important;
//   // }
// }

.gradientBtn {
  color: #fff;
  border-radius: 50px;
  line-height: 32px;
  background: linear-gradient(
    90deg,
    rgb(var(--gradient-color-primary1)) 0%,
    rgb(var(--gradient-color-primary2)) 100%
  );
}
.adm-button.adm-button-mini {
  padding: 4px 8px;
  min-height: 24px;
  line-height: 18px;
  font-size: var(--adm-font-size-3);
  
}

// Toast
.adm-toast-wrap {
  .adm-toast-main {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin: 0 auto;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 8px;
    padding: 12px 16px;
    
    .adm-toast-icon {
      font-size: 24px;
      margin-bottom: 0px;
     
    }
    
    .adm-auto-center {
      .adm-auto-center-content {
        color: #fff;
        font-size: 14px;
        line-height: 1.4;
        text-align: center;
      }
    }
  }
}

.announcementModal {
  .adm-center-popup-wrap {
    max-width: 80vw;
    width: 400px;
    overflow-x: visible;
  }

  .adm-modal-body:not(.adm-modal-with-image) {
    padding: 0px;
  }

  .adm-modal-body {
    overflow:visible;
  }

  .adm-modal-content {
    padding: 0px;
    overflow-y: visible;
    overflow-x: visible;
  }

  
}

.appVersionModal {
  .adm-modal-body:not(.adm-modal-with-image) {
    padding: 0px;
  }
}

.vip-modal {
  .adm-modal-button-primary {
    background-color: rgb(var(--icon-color-gold));
    --border-color: rgb(var(--icon-color-gold));
  }
}

/* --- Splash Screen Styles --- */
.splash-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  // Removed flex properties to allow image to fill screen
  // display: flex;
  // flex-direction: column;
  // justify-content: center;
  // align-items: center;
  background-color: rgb(var(--icon-color-primary)); // Fallback background
  z-index: 9999; // Ensure it's on top
}

/* --- End Splash Screen Styles --- */
