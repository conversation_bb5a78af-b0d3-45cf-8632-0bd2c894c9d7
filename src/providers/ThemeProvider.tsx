import {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from "react";
import { PrimaryThemeKey, primaryThemes } from "../config/themes";

// Define the shape of the theme context value
interface ThemeContextValue {
  primaryTheme: PrimaryThemeKey;
  updatePrimaryTheme: (theme: PrimaryThemeKey) => void;
}

// Create the theme context
const ThemeContext = createContext<ThemeContextValue | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

// Default theme if environment variable is not set or invalid
const DEFAULT_THEME: PrimaryThemeKey = "green"; // Or your preferred default

export const ThemeProvider = ({ children }: ThemeProviderProps) => {
  // Initialize theme state, prioritizing the environment variable
  const [primaryTheme, setPrimaryTheme] = useState<PrimaryThemeKey>(() => {
    const envTheme = import.meta.env.VITE_THEME_COLOR;

    const validThemes: PrimaryThemeKey[] = [
      "green",
      "blue",
      "orange",
      "black",
      "pink",
    ]; // Ensure this matches your theme keys

    if (envTheme && validThemes.includes(envTheme as PrimaryThemeKey)) {
      return envTheme as PrimaryThemeKey;
    }
    // Fallback to default theme if env var is not set or invalid
    return DEFAULT_THEME;
  });

  // Function to update the primary theme
  const updatePrimaryTheme = (theme: PrimaryThemeKey) => {
    console.log("updatePrimaryTheme", theme);
    setPrimaryTheme(theme);
  };

  // Context value
  const contextValue: ThemeContextValue = {
    primaryTheme,
    updatePrimaryTheme,
  };

  useEffect(() => {
    const colors = primaryThemes[primaryTheme];
    const cssVarMap: Record<string, string> = {
      textColorPrimary: "--text-color-primary",
      textColorPrimaryDark: "--text-color-primary-dark",
      iconColorPrimary: "--icon-color-primary",
      gradientColorPrimary1: "--gradient-color-primary1",
      gradientColorPrimary2: "--gradient-color-primary2",
    };

    // Set each CSS variable from the theme
    Object.entries(colors).forEach(([key, value]) => {
      // Skip themeHexColor as it's not a CSS variable
      if (key === "themeHexColor") return;

      // Get the corresponding CSS variable name, or use kebab case conversion as fallback
      const cssVarName =
        cssVarMap[key] || `--${key.replace(/([A-Z])/g, "-$1").toLowerCase()}`;

      document.documentElement.style.setProperty(cssVarName, value as string);
    });

    // If gradient-color-primary3 isn't defined in the theme, set a reasonable default
    // based on primary2 (or could be derived from other colors)
    if (!colors.hasOwnProperty("gradientColorPrimary3")) {
      document.documentElement.style.setProperty(
        "--gradient-color-primary3",
        "217 191 217" // Default value, you may want to compute this dynamically
      );
    }
  }, [primaryTheme]);

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

// Custom hook to use the theme context
export const useThemeContext = () => {
  const context = useContext(ThemeContext);

  if (context === undefined) {
    throw new Error("useThemeContext must be used within a ThemeProvider");
  }

  return context;
};
