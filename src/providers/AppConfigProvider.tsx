import { createContext, ReactNode, useContext, useState } from "react";
import { AppConfig } from "../types/api";

// Extended context to include update functions
interface AppConfigContextValue {
  config: AppConfig;
}

// Create context with undefined initial value
const AppConfigContext = createContext<AppConfigContextValue | undefined>(
  undefined
);

interface AppConfigProviderProps {
  children: ReactNode;
  config: AppConfig;
}

export const AppConfigProvider = ({
  children,
  config: initialConfig,
}: AppConfigProviderProps) => {
  // Initialize config state directly with the initialConfig prop
  const [config] = useState<AppConfig>(initialConfig);

  // Context value includes both the config and update functions
  const contextValue: AppConfigContextValue = {
    config,
  };

  return (
    <AppConfigContext.Provider value={contextValue}>
      {children}
    </AppConfigContext.Provider>
  );
};

// Custom hook to use the app configuration
export const useAppConfigContext = () => {
  const context = useContext(AppConfigContext);

  if (context === undefined) {
    throw new Error(
      "useAppConfigContext must be used within an AppConfigProvider"
    );
  }

  return context;
};
