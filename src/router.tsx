import { create<PERSON>rowser<PERSON>outer } from "react-router";
import Home from "./pages/main/Home";
import Login from "./pages/user/Login";
import SharedLayout from "./pages/main/SharedLayout";
import Welfare from "./pages/main/Welfare";
import Me from "./pages/main/Me";
import Theme from "./pages/main/Theme";
import Vip from "./pages/main/Vip";
import Setting from "./pages/user/Setting";
import PurchaseFlow from "./pages/user/PurchaseFlow";
import ChangePassword from "./pages/user/ChangePassword";
import BindPhone from "./pages/user/BindPhone";
// import AgentPromote from "./pages/user/AgentPromote";
// import ExchangeRecord from "./pages/user/ExchangeRecord";
// import MemberShowOrder from "./pages/user/MemberShowOrder";
// import PromotionalData from "./pages/user/PromotionalData";
// import PointsRedemption from "./pages/user/PointsRedemption";
// import ExchangeGoods from "./pages/user/ExchangeGoods";
import ThemeDetail from "./pages/main/modules/ThemeDetail";
import ThemeVideo from "./pages/main/modules/ThemeVideo";
import PurchasedTheme from "./pages/user/PurchasedTheme";
import MemberCenter from "./pages/user/MemberCenter";
import Payment from "./pages/user/Payment";
import ForgetPassword from "./pages/user/ForgetPassword";
import Register from "./pages/user/Register";
import CategoryList from "./pages/main/CategoryList";
import CategoryDetail from "./pages/main/CategoryDetail";
import ProtectedLayout from "./components/ProtectedLayout";
import UnauthenticatedLayout from "./components/UnauthenticatedLayout";
import ThemeExample from "./examples/ThemeExample";
import CategoryPage from "./pages/category/CategoryPage";

export const router = createBrowserRouter([
  {
    path: "/",
    element: <SharedLayout />,
    children: [
      {
        path: "/theme-example",
        element: <ThemeExample />,
      },
      {
        index: true,
        element: <Home />,
      },
      {
        path: "/welfare",
        element: <Welfare />,
      },
      {
        path: "/vip",
        element: <Vip />,
      },
      {
        path: "/theme",
        element: <Theme />,
      },
      {
        path: "/theme/detail/:topic_id",
        element: <ThemeDetail />,
      },
      {
        path: "/theme/detail/video",
        element: <ThemeVideo />,
      },
      {
        path: "/categories",
        element: <CategoryPage />,
      },
      {
        path: "/category-list/:name",
        element: <CategoryList />,
      },
      {
        path: "/category-detail/:category_name/:id",
        element: <CategoryDetail />,
      },
      {
        path: "/me",
        element: <ProtectedLayout />,
        children: [
          {
            index: true,
            element: <Me />,
          },
          {
            path: "setting",
            element: <Setting />,
          },
          {
            path: "change-password",
            element: <ChangePassword />,
          },
          {
            path: "bind-phone",
            element: <BindPhone />,
          },
          {
            path: "purchase-flow",
            element: <PurchaseFlow />,
          },
          {
            path: "purchased-theme",
            element: <PurchasedTheme />,
          },
          {
            path: "member-center",
            element: <MemberCenter />,
          },
          {
            path: "payment/:payment_id",
            element: <Payment />,
          },
        ],
      },
      {
        path: "/",
        element: <UnauthenticatedLayout />,
        children: [
          {
            path: "login",
            element: <Login />,
          },
          {
            path: "register",
            element: <Register />,
          },
          {
            path: "forget-password",
            element: <ForgetPassword />,
          },
        ],
      },
    ],
  },
]);
