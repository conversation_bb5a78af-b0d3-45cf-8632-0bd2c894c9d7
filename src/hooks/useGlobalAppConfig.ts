import { useAppConfigContext } from "../providers/AppConfigProvider";
import { useThemeContext } from "../providers/ThemeProvider";
import { primaryThemes, PrimaryThemeKey } from "../config/themes";

/**
 * Custom hook to access global app configuration
 * This hook provides access to app configuration data from anywhere in the component tree
 * without needing to fetch the data again through API calls.
 *
 * Also provides dynamic theme information for the application.
 */
export const useGlobalAppConfig = () => {
  const { config } = useAppConfigContext();
  const { primaryTheme, updatePrimaryTheme } = useThemeContext();

  // Get current primary theme colors
  const primaryThemeColors = primaryThemes[primaryTheme] || primaryThemes.green;

  return {
    ...config.config,
    primaryTheme,
    primaryThemeColors,
    getPrimaryThemeHexColor: () => primaryThemeColors.themeHexColor,
    updatePrimaryTheme,
  };
};
