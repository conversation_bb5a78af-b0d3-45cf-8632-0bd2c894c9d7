import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS, APP_KEY } from "../config/api-endpoints";
import { postRequest, handleMutationError } from "../utils/query";
import {
  ImportTopicPayload,
  ImportVideoPayload,
  ProductsListPayload,
  ProductsListResponse,
  TopicDetailsPayload,
  TopicDetailsResponse,
  TopicsListPayload,
  TopicsListResponse,
  VideoDetailsPayload,
  VideoDetailsResponse,
  VideosIndexPayload,
  VideosListPayload,
  VideosListResponse,
  VideosRelatedPayload,
  VideosRelatedResponse,
} from "../types/api";

// Video hooks
export const useHomeVideos = (menuId?: number, enabled = true) => {
  return useQuery({
    queryKey: ["homeVideos", menuId],
    queryFn: async () => {
      const payload: VideosIndexPayload = {
        app: APP_KEY,
      };

      if (menuId) {
        payload.menu_id = menuId;
      }

      return postRequest<any>(API_ENDPOINTS.appVideosIndex, payload);
    },
    enabled,
  });
};

/**
 * 获取视频列表
 * @param params
 *  menu_id?: number;
 *  type?: string; latest, popular
 *  duration_type?: string; long,short, all
 *  page?: number;
 *  limit?: number;
 *  category_id?: number;
 * @param enabled
 * @returns
 */

export const useVideosList = (
  params?: {
    menu_id?: number;
    type?: string;
    duration_type?: string;
    page?: number;
    limit?: number;
    category_id?: number;
  },
  enabled = true
) => {
  console.log("params", params);
  return useQuery({
    queryKey: ["videosList", params],
    queryFn: async () => {
      const payload: VideosListPayload = {
        app: APP_KEY,
        ...params,
      };

      return postRequest<VideosListResponse>(
        API_ENDPOINTS.appVideosList,
        payload
      );
    },
    enabled,
  });
};

export const useVideoDetails = (videoId: number, enabled = true) => {
  return useQuery({
    queryKey: ["videoDetails", videoId],
    queryFn: async () => {
      const payload: VideoDetailsPayload = {
        app: APP_KEY,
        video_id: videoId,
      };

      return postRequest<VideoDetailsResponse>(
        API_ENDPOINTS.appVideosDetails,
        payload
      );
    },
    enabled,
  });
};

/**
 * Get related videos for a specific video
 * @param videoId The ID of the video to get related videos for
 * @param params Additional parameters for pagination and filtering
 * @param enabled Whether the query should execute
 * @returns UseQuery result with related videos data
 */
export const useVideosRelated = (categoryId: number, enabled = true) => {
  return useQuery({
    queryKey: ["videosRelated", categoryId],
    queryFn: async () => {
      const payload: VideosRelatedPayload = {
        app: APP_KEY,
        category_id: categoryId,
      };

      return postRequest<VideosRelatedResponse>(
        API_ENDPOINTS.appVideosRelated,
        payload
      );
    },
    enabled,
  });
};

// Topic hooks
export const useTopicsList = (
  params?: { page?: number; limit?: number },
  enabled = true
) => {
  return useQuery({
    queryKey: ["topicsList", params],
    queryFn: async () => {
      const payload: TopicsListPayload = {
        app: APP_KEY,
        ...params,
      };

      return postRequest<TopicsListResponse>(
        API_ENDPOINTS.appTopicsList,
        payload
      );
    },
    enabled,
  });
};

export const useTopicDetails = (topicId: number | string, enabled = true) => {
  return useQuery({
    queryKey: ["topicDetails", topicId],
    queryFn: async () => {
      const payload: TopicDetailsPayload = {
        app: APP_KEY,
        topic_id: typeof topicId === "string" ? Number(topicId) : topicId,
      };

      return postRequest<TopicDetailsResponse>(
        API_ENDPOINTS.appTopicsDetails,
        payload
      );
    },
    enabled,
  });
};

// Product hooks
export const useProductsList = (
  params?: { page?: number; limit?: number },
  enabled = true
) => {
  return useQuery({
    queryKey: ["productsList", params],
    queryFn: async () => {
      const payload: ProductsListPayload = {
        app: APP_KEY,
        ...params,
      };

      return postRequest<ProductsListResponse>(
        API_ENDPOINTS.appProductsList,
        payload
      );
    },
    enabled,
  });
};

// Import hooks
export const useImportVideo = () => {
  return useMutation({
    mutationFn: async (videoData: ImportVideoPayload) => {
      return postRequest<any>(API_ENDPOINTS.importVideo, videoData);
    },
    onError: (error) => {
      return handleMutationError(error);
    },
  });
};

export const useImportTopic = () => {
  return useMutation({
    mutationFn: async (topicData: ImportTopicPayload) => {
      return postRequest<any>(API_ENDPOINTS.importTopic, topicData);
    },
    onError: (error) => {
      return handleMutationError(error);
    },
  });
};
