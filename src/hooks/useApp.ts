import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS, APP_KEY } from "../config/api-endpoints";
import { postRequest, handleMutationError } from "../utils/query";
import {
  AppConfig,
  AppConfigPayload,
  CreateOrderPayload,
  MemberTopicsListPayload,
  MenusResponse,
  NoticesPayload,
  OrdersListPayload,
  OrdersListResponse,
  BannersResponse,
  NoticesResponse,
  ScrollingMessagesResponse,
  PaymentGatewayListResponse,
  AppVersionsResponse,
} from "../types/api";
import { MemberTopicsResponse } from "../types/member";

// Config hooks
export const useAppConfig = (enabled = true) => {
  return useQuery({
    queryKey: ["appConfig"],
    queryFn: async () => {
      const payload: AppConfigPayload = { app: APP_KEY };
      const response = await postRequest<AppConfig>(
        API_ENDPOINTS.config,
        payload
      );
      return response;
    },
    enabled,
  });
};

export const useMenus = (enabled = true) => {
  return useQuery({
    queryKey: ["menus"],
    queryFn: async () => {
      return postRequest<MenusResponse>(API_ENDPOINTS.appMenus, {
        app: APP_KEY,
      });
    },
    enabled,
  });
};

export const useBanners = (enabled = true) => {
  return useQuery({
    queryKey: ["banners"],
    queryFn: async () => {
      return postRequest<BannersResponse>(API_ENDPOINTS.banners, {
        app: APP_KEY,
      });
    },
    enabled,
  });
};

/**
 * 获取公告
 * @param type 公告类型（1：首页公告，2：登入页公告）
 * @param enabled 是否启用
 * @returns
 */

export const useNotices = (type?: number, enabled = true) => {
  return useQuery({
    queryKey: ["notices", type],
    queryFn: async () => {
      const payload: NoticesPayload = { app: APP_KEY };
      if (type !== undefined) {
        payload.type = type;
      }
      return postRequest<NoticesResponse>(API_ENDPOINTS.notices, payload);
    },
    enabled,
  });
};

export const useScrollingMessages = (enabled = true) => {
  return useQuery({
    queryKey: ["scrollingMessages"],
    queryFn: async () => {
      return postRequest<ScrollingMessagesResponse>(
        API_ENDPOINTS.scrollingMessages,
        {
          app: APP_KEY,
        }
      );
    },
    enabled,
  });
};

// Order hooks
export const useOrdersList = (
  params?: { page?: number; limit?: number },
  enabled = true
) => {
  return useQuery({
    queryKey: ["ordersList", params],
    queryFn: async () => {
      const payload: OrdersListPayload = {
        app: APP_KEY,
        ...params,
      };
      return postRequest<OrdersListResponse>(
        API_ENDPOINTS.appOrdersList,
        payload
      );
    },
    enabled,
  });
};

export const useCreateOrder = () => {
  return useMutation({
    mutationFn: async (orderData: {
      product_id: number;
      payment_gateway_id: number;
      // 订单类型（1=VIP, 2=主题）.
      type: number;
    }) => {
      const payload: CreateOrderPayload = {
        app: APP_KEY,
        ...orderData,
      };
      return postRequest<any>(API_ENDPOINTS.appOrdersCreate, payload);
    },
    onError: (error) => {
      return handleMutationError(error);
    },
  });
};

export const useMemberTopics = (
  params?: { page?: number; limit?: number },
  enabled = true
) => {
  return useQuery<MemberTopicsResponse>({
    queryKey: ["memberTopics", params],
    queryFn: async () => {
      const payload: MemberTopicsListPayload = {
        app: APP_KEY,
        ...params,
      };
      return postRequest<MemberTopicsResponse>(
        API_ENDPOINTS.appMemberTopicsList,
        payload
      );
    },
    enabled,
  });
};

export const usePaymentGatewayList = (enabled = true) => {
  return useQuery({
    queryKey: ["paymentGateways"],
    queryFn: async () => {
      return postRequest<PaymentGatewayListResponse>(
        API_ENDPOINTS.paymentGatewayList
      );
    },
    enabled,
  });
};

// App Version hook
export const useAppVersions = (enabled = true) => {
  return useQuery({
    queryKey: ["appVersions"],
    queryFn: async () => {
      return postRequest<AppVersionsResponse>(API_ENDPOINTS.appVersions, {
        app: APP_KEY,
      });
    },
    enabled,
  });
};
