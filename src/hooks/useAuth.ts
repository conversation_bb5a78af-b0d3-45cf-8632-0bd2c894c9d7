import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS, APP_KEY } from "../config/api-endpoints";
import { postRequest, handleMutationError, getRequest } from "../utils/query";
import {
  BindPhonePayload,
  LoginPayload,
  LoginResponseData,
  PhoneLoginPayload,
  RegisterPayload,
  ResetForgotPasswordPayload,
  SendResetCodePayload,
  SendVerificationCodePayload,
  UpdatePasswordPayload,
  UserInfoResponse,
} from "../types/api";

// Login hook
export const useLogin = () => {
  return useMutation({
    mutationFn: async ({
      username,
      password,
      phone,
      countryCode,
    }: {
      username: string;
      password: string;
      phone?: string;
      countryCode?: string;
    }) => {
      const payload: LoginPayload = {
        app: APP_KEY,
        username,
        password,
      };
      console.log("payload", payload);
      return postRequest<LoginResponseData>(API_ENDPOINTS.login, payload);
    },
    onSuccess: (data) => {
      // Only store the access token
      if (data.access_token) {
        localStorage.setItem("access_token", data.access_token);
      }
    },
    onError: (error) => {
      return handleMutationError(error);
    },
  });
};

// Phone Login hook
export const usePhoneLogin = () => {
  return useMutation({
    mutationFn: async ({
      phone,
      countryCode,
      password,
    }: {
      phone: string;
      countryCode: string;
      password: string;
    }) => {
      const payload: PhoneLoginPayload = {
        app: APP_KEY,
        phone,
        country_code: countryCode,
        password,
      };

      return postRequest<LoginResponseData>(API_ENDPOINTS.phoneLogin, payload);
    },
    onSuccess: (data) => {
      // Only store the access token
      if (data.access_token) {
        localStorage.setItem("access_token", data.access_token);
      }
    },
    onError: (error) => {
      return handleMutationError(error);
    },
  });
};

// Register hook
export const useRegister = () => {
  return useMutation({
    mutationFn: async ({
      username,
      password,
      passwordConfirmation,
      inviteCode,
    }: {
      username: string;
      password: string;
      passwordConfirmation: string;
      inviteCode?: string;
    }) => {
      const payload: RegisterPayload = {
        app: APP_KEY,
        username,
        password,
        password_confirmation: passwordConfirmation,
      };

      if (inviteCode) {
        payload.invite_code = inviteCode;
      }

      return postRequest<LoginResponseData>(API_ENDPOINTS.register, payload);
    },
    onSuccess: (data) => {
      // Only store the access token
      if (data.access_token) {
        localStorage.setItem("access_token", data.access_token);
      }
    },
    onError: (error) => {
      return handleMutationError(error);
    },
  });
};

// Send reset code hook
export const useSendResetCode = () => {
  return useMutation({
    mutationFn: async ({
      countryCode,
      phone,
    }: {
      countryCode: string;
      phone: string;
    }) => {
      const payload: SendResetCodePayload = {
        app: APP_KEY,
        country_code: countryCode,
        phone,
      };

      return postRequest<any>(API_ENDPOINTS.sendResetCode, payload);
    },
    onError: (error) => {
      return handleMutationError(error);
    },
  });
};

// Update password hook
export const useUpdatePassword = () => {
  return useMutation({
    mutationFn: async ({
      oldPassword,
      newPassword,
      passwordConfirmation,
    }: {
      oldPassword: string;
      newPassword: string;
      passwordConfirmation: string;
    }) => {
      const payload: UpdatePasswordPayload = {
        old_password: oldPassword,
        new_password: newPassword,
        password_confirmation: passwordConfirmation,
      };

      return postRequest<any>(API_ENDPOINTS.updatePassword, payload);
    },
    onError: (error) => {
      return handleMutationError(error);
    },
  });
};

// Send verification code hook
export const useSendVerificationCode = () => {
  return useMutation({
    mutationFn: async ({
      countryCode,
      phone,
    }: {
      countryCode: string;
      phone: string;
    }) => {
      const payload: SendVerificationCodePayload = {
        app: APP_KEY,
        country_code: countryCode,
        phone,
      };

      return postRequest<any>(API_ENDPOINTS.sendVerificationCode, payload);
    },
    onError: (error) => {
      return handleMutationError(error);
    },
  });
};

// Bind phone hook
export const useBindPhone = () => {
  return useMutation({
    mutationFn: async ({
      countryCode,
      phone,
      code,
    }: {
      countryCode: string;
      phone: string;
      code: string;
    }) => {
      const payload: BindPhonePayload = {
        app: APP_KEY,
        country_code: countryCode,
        phone,
        code,
      };

      return postRequest<any>(API_ENDPOINTS.bindPhone, payload);
    },
    onError: (error) => {
      return handleMutationError(error);
    },
  });
};

// Get user info hook
export const useUserInfo = (enabled = true) => {
  return useQuery({
    queryKey: ["userInfo"],
    queryFn: async () => {
      return postRequest<UserInfoResponse>(API_ENDPOINTS.me, {
        include: "application",
      });
    },
    enabled,
  });
};

// Reset forgot password hook
export const useResetForgotPassword = () => {
  return useMutation({
    mutationFn: async ({
      countryCode,
      phone,
      code,
      password,
      passwordConfirmation,
    }: {
      countryCode: string;
      phone: string;
      code: string;
      password: string;
      passwordConfirmation: string;
    }) => {
      const payload: ResetForgotPasswordPayload = {
        app: APP_KEY,
        country_code: countryCode,
        phone,
        code,
        password,
        password_confirmation: passwordConfirmation,
      };

      return postRequest<any>(API_ENDPOINTS.resetForgotPassword, payload);
    },
    onError: (error) => {
      return handleMutationError(error);
    },
  });
};
