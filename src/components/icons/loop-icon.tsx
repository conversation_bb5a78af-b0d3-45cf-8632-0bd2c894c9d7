import React from "react";
import { useGlobalAppConfig } from "../../hooks/useGlobalAppConfig"; // Adjust path if needed

interface LoopIconProps {
  size?: number;
  className?: string;
  color?: string; // Allow overriding color if needed
}

const LoopIcon: React.FC<LoopIconProps> = ({ size = 30, className = "" }) => {
  const { getPrimaryThemeHexColor } = useGlobalAppConfig();
  const primaryColor = getPrimaryThemeHexColor(); // Use prop color or theme color

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 30 30"
      fill="none"
      className={className}
    >
      <g clipPath="url(#clip0_10_24321_loop)">
        {" "}
        {/* Added suffix to avoid ID clashes */}
        <path
          d="M21.1072 17.1966C20.8135 16.9169 20.4222 16.7632 20.0168 16.7681C19.6113 16.7731 19.2239 16.9363 18.9372 17.223C18.6505 17.5098 18.4872 17.8972 18.4823 18.3026C18.4773 18.7081 18.6311 19.0994 18.9107 19.393L20.1268 20.6091H8.27679C6.90473 20.6077 5.58928 20.062 4.61908 19.0918C3.64889 18.1216 3.10321 16.8061 3.10179 15.4341C3.10156 14.2472 3.4418 13.0852 4.08215 12.0859C4.30451 11.7399 4.38032 11.3198 4.29291 10.9179C4.20551 10.516 3.96204 10.1654 3.61608 9.94301C3.27011 9.72065 2.84999 9.64484 2.44812 9.73224C2.04626 9.81965 1.69558 10.0631 1.47322 10.4091C0.509319 11.9076 -0.00217247 13.6523 6.93567e-06 15.4341C0.00284192 17.6283 0.875768 19.7319 2.42735 21.2835C3.97894 22.8351 6.08252 23.708 8.27679 23.7109H19.9661L18.8946 24.7823C18.63 25.0785 18.4889 25.4649 18.5003 25.862C18.5117 26.259 18.6748 26.6366 18.9561 26.9172C19.2373 27.1977 19.6153 27.3599 20.0124 27.3703C20.4095 27.3808 20.7955 27.2387 21.0911 26.9734L24.8732 23.1912C25.0172 23.0474 25.1315 22.8767 25.2094 22.6887C25.2873 22.5007 25.3275 22.2992 25.3275 22.0957C25.3275 21.8922 25.2873 21.6907 25.2094 21.5027C25.1315 21.3147 25.0172 21.1439 24.8732 21.0002L21.1072 17.1966Z"
          fill={primaryColor} // Use dynamic color
        />
        <path
          d="M21.7232 6.64301H9.00001L10.4089 5.23408C10.6736 4.93784 10.8147 4.55148 10.8033 4.15442C10.7918 3.75735 10.6288 3.37974 10.3475 3.0992C10.0663 2.81866 9.6883 2.6565 9.29121 2.64605C8.89411 2.6356 8.5081 2.77765 8.21251 3.04301L4.43036 6.82515C4.28636 6.96895 4.17213 7.13972 4.09418 7.3277C4.01624 7.51568 3.97612 7.71719 3.97612 7.92069C3.97612 8.12419 4.01624 8.32569 4.09418 8.51368C4.17213 8.70166 4.28636 8.87243 4.43036 9.01622L8.21251 12.7984C8.5081 13.0637 8.89411 13.2058 9.29121 13.1953C9.6883 13.1849 10.0663 13.0227 10.3475 12.7422C10.6288 12.4616 10.7918 12.084 10.8033 11.687C10.8147 11.2899 10.6736 10.9035 10.4089 10.6073L9.54644 9.7448H21.7232C23.0953 9.74621 24.4107 10.2919 25.3809 11.2621C26.3511 12.2323 26.8968 13.5477 26.8982 14.9198C26.8993 16.1379 26.5395 17.3291 25.8643 18.343C25.6402 18.686 25.5606 19.1035 25.6429 19.5049C25.7252 19.9062 25.9626 20.2588 26.3036 20.4859C26.5583 20.6529 26.8561 20.7423 27.1607 20.743C27.4168 20.7432 27.6689 20.6796 27.8943 20.5581C28.1197 20.4365 28.3113 20.2607 28.4518 20.0466C29.4623 18.5278 30.001 16.744 30 14.9198C29.9972 12.7255 29.1242 10.6219 27.5727 9.07036C26.0211 7.51877 23.9175 6.64584 21.7232 6.64301Z"
          fill={primaryColor} // Use dynamic color
        />
      </g>
      <defs>
        {/* Added suffix to avoid ID clashes if multiple icons are on the page */}
        <clipPath id="clip0_10_24321_loop">
          <rect width="30" height="30" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default LoopIcon;
