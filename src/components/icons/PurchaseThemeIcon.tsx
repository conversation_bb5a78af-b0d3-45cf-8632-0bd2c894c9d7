import React from "react";
import { useGlobalAppConfig } from "../../hooks/useGlobalAppConfig";

interface PurchaseThemeIconProps {
  size?: number;
  className?: string;
  color?: string;
}

const PurchaseThemeIcon: React.FC<PurchaseThemeIconProps> = ({
  size = 30,
  className = "",
  color,
}) => {
  const { getPrimaryThemeHexColor } = useGlobalAppConfig();
  const iconColor = color || getPrimaryThemeHexColor() || "#000000";

  return (
    // --- PASTE YOUR SVG CONTENT FROM purhase-theme.svg HERE ---
    <svg
      width={size}
      height={size}
      viewBox="0 0 30 30" // Adjust if SVG is different
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      {/* Replace with actual paths from purhase-theme.svg, applying iconColor to fill */}
      <g clipPath="url(#clip0_19_17005_purchase)">
        {" "}
        {/* Unique clipPath ID */}
        <path
          d="M14.9998 0C6.74981 0 0 6.75021 0 15.0002C0 23.2502 6.75061 30 14.9998 30C23.249 30 30 23.2502 30 15.0002C30 6.75021 23.2498 0 14.9998 0ZM27 9.44989L21.3 15.0002V3.45117C23.6998 4.80002 25.8001 6.89995 27 9.44989ZM18.1499 18.1503C17.4 19.0499 16.1985 19.4999 14.9998 19.4999C13.8011 19.4999 12.6 19.0499 11.8497 18.1503C10.9501 17.4 10.5001 16.1989 10.5001 15.0002C10.5001 12.4514 12.4514 10.5001 15.0002 10.5001C17.549 10.5001 19.5003 12.4514 19.5003 15.0002C19.4999 16.2001 19.0499 17.4 18.1499 18.1503ZM19.4999 2.54995V10.5001L11.2507 2.40021C12.4507 2.09993 13.6506 1.80006 15.0006 1.80006C16.6497 1.79846 18.1499 2.09993 19.4999 2.54995ZM9.44989 2.99996L14.9998 8.7H3.45077C4.79922 6.2986 6.89995 4.19986 9.44989 2.99996ZM2.54995 10.5001H10.5001L2.25047 18.75C1.95059 17.5501 1.65071 16.3502 1.65071 15.0002C1.79992 13.3504 2.09967 11.8504 2.54995 10.5001ZM2.99996 20.5501L8.70199 15.0002V26.5492C6.2998 25.2 4.20186 23.1001 2.99996 20.5501ZM10.5001 27.4501V19.4999L18.7501 27.7503C17.5497 28.0502 16.3498 28.3501 14.9998 28.3501C13.35 28.2003 11.8501 27.9003 10.5001 27.4501ZM20.5501 27L14.9998 21.3H26.5488C25.2 23.7018 23.1001 25.8001 20.5501 27ZM27.4501 19.4999H19.4999L27.7499 11.25C28.0498 12.4503 28.3501 13.6502 28.3501 15.0002C28.2001 16.6503 27.9001 18.1502 27.4501 19.4999Z"
          fill={iconColor}
        />
      </g>
      <defs>
        <clipPath id="clip0_19_17005_purchase">
          {" "}
          {/* Unique clipPath ID */}
          <rect width="30" height="30" fill="white" />
        </clipPath>
      </defs>
    </svg>
    // --- END OF SVG CONTENT ---
  );
};

export default PurchaseThemeIcon;
