import { useVideosList } from "@/hooks/useContent";
import { useEffect, useState } from "react";
import { DotLoading, InfiniteScroll, List } from "antd-mobile";
import { Video } from "@/types/api";
import VideoCard from "@/components/common/VideoCard";

interface VideosListProps {
  categoryId?: number;
}

const VideosList = ({ categoryId }: VideosListProps) => {
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [videoItems, setVideoItems] = useState<Video[]>([]);

  const { data, isLoading, isFetching, isError, error } = useVideosList({
    page,
    category_id: categoryId,
  });

  useEffect(() => {
    // Reset when category changes
    setPage(1);
    setVideoItems([]);
    setHasMore(true);
  }, [categoryId]);

  useEffect(() => {
    if (data?.videos?.data) {
      if (page === 1) {
        setVideoItems(data.videos.data);
      } else {
        setVideoItems((prev) => [...prev, ...data.videos.data]);
      }

      setHasMore(!!data.videos.next_page_url);
    }
  }, [data, page]);

  const loadMore = async () => {
    if (hasMore && !isFetching) {
      setPage((prev) => prev + 1);
    }
  };

  const handleVideoClick = (video: Video) => {
    // Handle video click - navigation to details page
    console.log("Video clicked:", video.id);
  };

  if (isLoading && page === 1) {
    return (
      <div className="flex justify-center items-center py-12">
        <DotLoading color="primary" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="text-center py-8 text-red-500">
        {(error as Error)?.message || "Failed to load videos"}
      </div>
    );
  }

  return (
    <div className="video-list">
      <List>
        {videoItems.map((video) => (
          <List.Item key={video.id} clickable>
            <VideoCard video={video} onClick={handleVideoClick} />
          </List.Item>
        ))}
      </List>

      <InfiniteScroll loadMore={loadMore} hasMore={hasMore} />
    </div>
  );
};

export default VideosList;
