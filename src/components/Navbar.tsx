import React from "react";
import { useNavigate, useLocation } from "react-router";

interface INavbar {
  title: string;
  addOnAfter?: React.ReactNode;
  isVip?: boolean;
}

const Navbar = ({ title, addOnAfter, isVip }: INavbar) => {
  const navigate = useNavigate();

  const location = useLocation();
  const gradientClass = isVip ? "gradient-gold" : "gradient-primary";

  const handleGoBack = () => {
    // Check if we can go back (history length > 1)
    if (window.history.length <= 1) {
      // No history, go to homepage
      navigate("/");
      return;
    }

    // If there's a referrer and it's from a different host
    console.log(window.history.state);
    if (!window.history.state.idx) {
      // External referrer, navigate to home
      navigate("/");
    } else {
      // Internal navigation, go back
      navigate(-1);
    }
  };

  return (
    <div
      className={`lg:max-w-[30vw] mx-auto fixed top-0 left-0 right-0 z-50 h-12 ${gradientClass}`}
    >
      {/* <img
      className="w-full h-full absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
      src="/assets/images/header-bg.png"
      alt="bg"
    /> */}
      <div className="flex items-center w-full absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
        {/* Navbar - Back Icon */}
        <div
          className="w-4 h-4 absolute top-1/2 left-[20px] -translate-x-1/2 -translate-y-1/2 cursor-pointer"
          onClick={handleGoBack}
        >
          <img
            className="w-full h-full object-contain"
            src="/assets/images/arrow-left.png"
            alt="back"
          />
        </div>
        {/* Navbar - Title */}
        <div className="flex-[11] text-center text-xl text-white">
          <h2 className="truncate whitespace-nowrap overflow-hidden px-8">
            {title}
          </h2>
        </div>
      </div>
      {addOnAfter}
    </div>
  );
};

export default Navbar;
