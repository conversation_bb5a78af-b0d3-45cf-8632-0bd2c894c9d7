import { ReactNode, useState, useEffect } from "react";
import { useAppConfig, useAppVersions } from "../hooks/useApp";
import { AppConfigProvider } from "../providers/AppConfigProvider";
import SplashScreen from "./SplashScreen";
import UpdateModal from "./UpdateModal";

interface AppInitProps {
  children: ReactNode;
}

const AppInit = ({ children }: AppInitProps) => {
  const { data, isLoading, isError, error } = useAppConfig();
  const { data: appVersion } = useAppVersions();
  const [isUpdateModalVisible, setIsUpdateModalVisible] = useState(false);

  useEffect(() => {
    if (appVersion) {
      const version_name = appVersion.appVersions.version_name;
      // get the package.json version
      // for eg "1.0.1" (version_name) > "1.0.0" (packageJsonVersion), then we need to show the update modal

      const packageJsonVersion = process.env.APP_VERSION;
      if (version_name > packageJsonVersion) {
        setIsUpdateModalVisible(true);
      }
    }
  }, [data]);

  if (isLoading) {
    return <SplashScreen />;
  }

  if (isError) {
    return (
      <div className="flex flex-col justify-center items-center h-dvh">
        <p className="text-red-500 text-center">
          Failed to load app configuration
        </p>
        <p className="text-sm text-center mt-2">
          {(error as Error)?.message || "Unknown error"}
        </p>
        <button
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-md"
          onClick={() => window.location.reload()}
        >
          Retry
        </button>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="flex flex-col justify-center items-center h-dvh">
        <p className="text-red-500 text-center">
          No configuration data received
        </p>
        <button
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded-md"
          onClick={() => window.location.reload()}
        >
          Retry
        </button>
      </div>
    );
  }

  const handleCloseUpdateModal = () => {
    setIsUpdateModalVisible(false);
  };

  const handleUpdateNow = () => {
    window.open(appVersion?.appVersions.download_url, "_blank");
  };

  return (
    <AppConfigProvider config={data}>
      {children}
      {data && (
        <UpdateModal
          visible={isUpdateModalVisible}
          version={data.latestVersion || "1.01"}
          onClose={handleCloseUpdateModal}
          onUpdate={handleUpdateNow}
        />
      )}
    </AppConfigProvider>
  );
};

export default AppInit;
