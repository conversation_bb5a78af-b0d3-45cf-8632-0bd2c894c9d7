import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "antd-mobile";

interface UpdateModalProps {
  visible: boolean;
  version: string; // Example: '1.01'
  message?: string; // Optional custom message
  onClose: () => void; // Handler for 'Update Later'
  onUpdate: () => void; // Handler for 'Update Now'
}

const UpdateModal: React.FC<UpdateModalProps> = ({
  visible,
  version,
  message = `尊敬的用户, 现程序最新版本为${version}版本, 为了您更好的体验请点击更新。`,
  onClose,
  onUpdate,
}) => {
  return (
    <Modal
      visible={visible}
      content={
        <div className="text-center">
          {/* Header Image */}
          <div
            className="h-[120px] bg-cover bg-center rounded-t-lg -mx-4 -mt-4 mb-4"
            style={{ backgroundImage: "url(/assets/images/maintain.png)" }}
          ></div>
          {/* Title */}
          <h3 className="text-lg font-semibold text-green-600 mb-2">
            系统更新
          </h3>
          {/* Message */}
          <p className="text-sm text-gray-600 mb-6 px-2">{message}</p>
        </div>
      }
      actions={[
        {
          key: "later",
          text: <span className="text-gray-600">稍后更新</span>,
          style: {
            backgroundColor: "#f0f0f0", // Light gray background
            borderRadius: "20px",
            marginRight: "8px", // Space between buttons
            padding: "10px 16px",
            border: "none",
            boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
          },
          onClick: onClose,
        },
        {
          key: "update",
          text: <span className="text-white">立即更新</span>,
          style: {
            background: "linear-gradient(to right, #65d3ac, #4ac0a0)", // Green gradient
            borderRadius: "20px",
            marginLeft: "8px", // Space between buttons
            padding: "10px 16px",
            border: "none",
            boxShadow: "0 2px 4px rgba(0,0,0,0.15)",
          },
          onClick: onUpdate,
        },
      ]}
      closeOnAction
      bodyClassName="pb-0 rounded-lg" // Adjust padding and ensure rounded corners apply
      className="appVersionModal" // Add a class for potential global styling
      style={{ "--border-radius": "16px" } as React.CSSProperties} // Apply border-radius to the whole modal and cast type
    />
  );
};

export default UpdateModal;
