import { Button, Modal } from "antd-mobile";

interface ITermAndConditionModal {
  visible: boolean;
  setVisible: (visiblae: boolean) => void;
}

const TermAndConditionModal = ({
  visible,
  setVisible,
}: ITermAndConditionModal) => {
  return (
    <Modal
      visible={visible}
      className="customModal"
      content={
        <div>
          {/* Header */}
          <div className="text-center text-disabledIconGrey">
            <h3 className="text-lg font-medium">服务条款及隐私政策</h3>
          </div>
          {/* Body */}
          <div className="text-center text-disabledIconGrey flex flex-col gap-4">
            <p className="text-sm">
              在您注册成为顺丰会员的过程中，您需要通过点击同意的形式在线签署
              <span className="text-textColorPrimary">
                《猫咪视频用户服务协议》
              </span>
              与
              <span className="text-textColorPrimary">
                《猫咪视频隐私政策》
              </span>
              ，
              请您务必仔细阅读、充分理解条款内容后再点击同意(尤其是以粗体并下划线标识的条款，因为这些条款可能会明确您应履行的义务或对您的权利有所限制)。
            </p>
            <p>
              请您注意:如果您不同意上述服务条款、保护政策或其中任何约定，请您停止注册。如您阅读并点击同意即表示您已充分阅读、理解并接受其全部内容，并表明您也同意顺丰可以依据以上隐私政策来处理您的个人信息。如您对以上服务条款、隐私政策有任何疑问，您可联系猫咪客服。
            </p>
            <div>
              <p className="font-medium">点击同意即表示您已阅读并同意</p>
              <ul className="text-textColorPrimary underline">
                <li>《猫咪视频用户服务协议》</li>
                <li>《猫咪视频隐私政策》</li>
                <li>《儿童个人信息保护规则》</li>
              </ul>
            </div>
          </div>
          {/* Footer */}
          <div className="flex items-center gap-4 mt-5">
            <Button
              fill="solid"
              className="disabledBtn w-full"
              onClick={() => {
                setVisible(false);
              }}
            >
              取消
            </Button>
            <Button fill="solid" className="gradientBtn w-full">
              同意
            </Button>
          </div>
        </div>
      }
      closeOnAction
      showCloseButton={true}
      onClose={() => {
        setVisible(false);
      }}
    />
  );
};

export default TermAndConditionModal;
