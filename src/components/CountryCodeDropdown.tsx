import React, { FC, useState } from "react";

const areaCodes = [
  {
    id: 1,
    label: "中国",
    value: "+86",
  },
  {
    id: 2,
    label: "阿富汗",
    value: "+93",
  },
  {
    id: 3,
    label: "阿尔巴尼亚",
    value: "+355",
  },
  {
    id: 4,
    label: "阿尔及利亚",
    value: "+213",
  },
  {
    id: 5,
    label: "美属萨摩亚",
    value: "+684",
  },
];

interface ICountryCodeDropdown {
  onChange?: (val: string) => void;
  defaultValue?: string;
}

const CountryCodeDropdown: FC<ICountryCodeDropdown> = ({
  onChange,
  defaultValue,
}) => {
  const [toggleDropdownActive, setToggleDropdownActive] = useState(false);
  const defaultItem = defaultValue
    ? areaCodes.find((item) => item.value === defaultValue)
    : areaCodes[0];

  const [selectedValue, setSelectedValue] = useState(
    `${defaultItem?.value}${defaultItem?.label}`
  );

  const handleDropdownValue = (item: (typeof areaCodes)[0]) => {
    if (onChange && item) {
      onChange(item.value);
      setSelectedValue(`${item.value}${item.label}`);
    }
  };

  return (
    <div className="table-cell align-middle">
      <div
        className={`inline-block relative min-w-[90px]`}
        onClick={() => setToggleDropdownActive((prev: any) => !prev)}
      >
        <div className="min-w-[85px] w-max bg-textColorPrimary inline-block text-center py-[2px] px-[10px] rounded-[5px] cursor-pointer shadow">
          <span className="text-sm text-white">{selectedValue}</span>
          <img
            src="/assets/images/dropdown-arrow-down.png"
            alt="arrow"
            width={10}
            className={`inline ml-1 ${
              toggleDropdownActive === true ? "rotate-180" : ""
            }`}
          />
        </div>
        <div
          className={`dropDownMenuBox z-10 ${
            toggleDropdownActive === true
              ? "visible opacity-100"
              : "invisible opacity-0"
          }`}
        >
          <ul className="m-0 px-3 list-none overflow-y-scroll max-h-[350px] max-w-[180px]">
            {areaCodes?.map((item: any) => {
              return (
                <li
                  className="text-sm py-3 text-left relative hover:text-iconColorPrimary last-of-type:border-b-0"
                  onClick={() => handleDropdownValue(item)}
                  key={item.id}
                >
                  {item.value} {item.label}
                </li>
              );
            })}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default CountryCodeDropdown;
