import { useNotices, useScrollingMessages } from "@/hooks/useApp";
import { NoticeBar } from "antd-mobile";
import { Notice, ScrollingMessage } from "@/types/api";
import { useLocation } from "react-router";

const MarqueeNoticeBar = () => {
  const { data: scrollingMessages } = useScrollingMessages();

  const { pathname } = useLocation();
  const isVipPage = pathname === "/vip";

  // Process scrolling messages
  const scrollingContent = scrollingMessages?.scrolling_messages
    ?.filter(
      (message: ScrollingMessage) => message.title && message.status === 1
    )
    ?.map((message: ScrollingMessage) => message.title)
    ?.join(
      "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"
    ); // Add some spacing between messages

  // Combine content or use scrolling messages as priority
  const displayContent = scrollingContent;

  if (!displayContent) return null;

  return (
    <div className="relative my-5">
      <NoticeBar
        className={`noticeBar ${isVipPage ? "vip-notice" : ""}`}
        content={<div dangerouslySetInnerHTML={{ __html: displayContent }} />}
        icon={
          <div
            className={`${
              isVipPage ? "bg-textColorGold" : "bg-iconColorPrimary"
            } w-[40px] h-[40px] rounded-xl -ml-1 transition-colors duration-300`}
          >
            <img
              className="w-full h-full object-contain p-2"
              src="assets/images/alert.png"
              alt="noticeBar-icon"
            />
          </div>
        }
      />
    </div>
  );
};

export default MarqueeNoticeBar;
