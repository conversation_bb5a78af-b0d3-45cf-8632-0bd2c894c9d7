import React from "react";
import { useGlobalAppConfig } from "../hooks/useGlobalAppConfig";

interface VipLogoProps {
  backgroundColor?: string;
  size?: number;
  className?: string;
}

const VipLogo: React.FC<VipLogoProps> = ({
  backgroundColor,
  size = 100,
  className = "",
}) => {
  const { primaryThemeColors, getPrimaryThemeHexColor } = useGlobalAppConfig();

  // Use provided backgroundColor or get from theme
  const bgColor = backgroundColor || getPrimaryThemeHexColor();

  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <circle cx="50" cy="50" r="50" fill={bgColor} />
      <path
        d="M82.7791 26.4595C82.173 26.4595 81.5794 26.6325 81.0681 26.958L66.7872 36.0515L52.0057 17.2187C51.7077 16.839 51.3272 16.532 50.8931 16.3208C50.459 16.1097 49.9826 16 49.4999 16C49.0172 16 48.5408 16.1097 48.1067 16.3208C47.6726 16.532 47.2922 16.839 46.9941 17.2187L32.2126 36.0515L17.9317 26.958C17.4204 26.6325 16.8268 26.4596 16.2207 26.4595C13.8934 26.4595 12 28.3224 12 30.6121C12 30.7946 12.0157 30.9768 12.0469 31.1565L20.6436 80.7099C20.8345 82.827 22.6471 84.4924 24.8477 84.4924H74.1523C76.353 84.4924 78.1656 82.827 78.3565 80.7099L86.9532 31.1565C86.9843 30.9768 87 30.7945 87 30.6121C86.9998 28.3224 85.1064 26.4595 82.7791 26.4595ZM61.495 53.9972L52.2094 68.8576C51.6052 69.8247 50.5669 70.3555 49.5049 70.3555L49.4999 70.3554L49.4949 70.3555C48.4328 70.3555 47.3947 69.8247 46.7904 68.8576L37.5048 53.9972C36.5725 52.5051 37.0263 50.5399 38.5183 49.6077C40.0102 48.6754 41.9755 49.1292 42.9077 50.6211L49.4999 61.171L56.092 50.6211C57.0243 49.1291 58.9895 48.6754 60.4815 49.6077C61.9735 50.5399 62.4272 52.5052 61.495 53.9972Z"
        fill="white"
      />
    </svg>
  );
};

export default VipLogo;
