import React from "react";
// Removed: import { SpinLoading } from "antd-mobile";

const SplashScreen: React.FC = () => {
  return (
    <div className="splash-screen relative w-full h-screen">
      {/* Background Image */}
      <img
        src={" /assets/images/splash.png"}
        alt="Loading Splash Background"
        className="absolute inset-0 w-full h-full object-cover"
      />
      {/* Centered Logo and App Name */}
      <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-10 flex flex-col items-center">
        <img
          src={"/assets/images/logo.png"} // Placeholder logo path
          alt="App Logo"
          className="max-w-[30vw] max-h-[15vh] object-contain" // Reduced max width and height
        />
        {/* App Name */}
        <p className="mt-4 text-white text-2xl font-medium">
          {" "}
          {/* Increased text size from lg to 2xl */}
          {import.meta.env.VITE_APP_NAME || "App Name"}{" "}
          {/* Display App Name from env, fallback */}
        </p>
      </div>
      {/* Removed loading text */}
      {/* <div className="splash-screen-text">Loading...</div> */}
    </div>
  );
};

export default SplashScreen;
