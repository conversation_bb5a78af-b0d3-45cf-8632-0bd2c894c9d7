import { Helmet } from "react-helmet";

export const themeColors = {
  primary: "#4AB087", // Primary gradient start color (--gradient-color-primary1)
  gold: "#F4E3C2", // Gold gradient start color
  error: "#FF4D4F", // Error color
  success: "#52C41A", // Success color
} as const;

type ThemeColorProps = {
  color: string | keyof typeof themeColors;
};

export const ThemeColor = ({ color }: ThemeColorProps) => {
  const themeColor =
    color in themeColors
      ? themeColors[color as keyof typeof themeColors]
      : color;

  return (
    <Helmet>
      <meta
        name="theme-color"
        media="(prefers-color-scheme: light)"
        content={themeColor}
      />
      <meta
        name="theme-color"
        media="(prefers-color-scheme: dark)"
        content={themeColor}
      />
    </Helmet>
  );
};
