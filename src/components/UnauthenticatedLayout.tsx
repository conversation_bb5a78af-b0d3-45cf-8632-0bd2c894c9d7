import { Navigate, Outlet, useLocation } from "react-router";
import { useUser } from "../contexts/UserContext";
import { PageLoader } from "./common";

const UnauthenticatedLayout = () => {
  const location = useLocation();
  const { isAuthenticated, isLoading } = useUser();

  if (isLoading) {
    return <PageLoader />;
  }

  // If authenticated, redirect to home page
  if (isAuthenticated) {
    // Get the intended destination or default to home
    const to = location.state?.from?.pathname || "/";
    return <Navigate to={to} replace />;
  }

  // Not authenticated, allow access to login/register pages
  return <Outlet />;
};

export default UnauthenticatedLayout;
