import React from "react";

interface LoaderProps {
  text?: string;
}

const Loader: React.FC<LoaderProps> = ({ text = "" }) => {
  return (
    <div className="flex flex-col items-center justify-center h-full py-10">
      <img
        src="/assets/images/page-loader.png"
        alt="Loading..."
        className="w-32 mb-4 object-cover"
      />
      <p className="text-textColorPrimary text-base">{text}</p>
    </div>
  );
};

export default Loader;
