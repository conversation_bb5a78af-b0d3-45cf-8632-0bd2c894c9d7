import React from "react";

interface NoRecordProps {
  text?: string;
}

const NoRecord: React.FC<NoRecordProps> = ({ text = "" }) => {
  return (
    <div className="flex flex-col items-center justify-center h-full py-10">
      <img
        src="/assets/images/no-record.png"
        alt="No records"
        className="w-32 mb-4 object-cover"
      />
      <p className="text-textColorPrimary text-base">{text}</p>
    </div>
  );
};

export default NoRecord;
