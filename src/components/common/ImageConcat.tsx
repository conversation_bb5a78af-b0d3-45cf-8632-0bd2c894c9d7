import { FC, ImgHTMLAttributes } from "react";
import { useGlobalAppConfig } from "@/hooks/useGlobalAppConfig";

interface IImage extends Omit<ImgHTMLAttributes<HTMLImageElement>, "src"> {
  srcValue: string;
  onClick?: (value: any) => void;
  isVideoThumbnail?: boolean;
  aspectRatio?: string;
  className?: string;
}

const ImageConcat: FC<IImage> = ({
  srcValue = "",
  onClick = () => {},
  isVideoThumbnail = false,
  aspectRatio = "1/1",
  className = "",
  ...rest
}) => {
  const appConfig = useGlobalAppConfig();
  const placeholderImage = isVideoThumbnail
    ? "/assets/images/video-placeholder.png"
    : "";

  // Handle empty source value
  if (!srcValue) {
    return isVideoThumbnail ? (
      <img
        src={placeholderImage}
        className={className}
        {...rest}
        onClick={onClick}
      />
    ) : null;
  }

  // Handle absolute URLs directly
  if (srcValue.includes("https://") || srcValue.includes("http://")) {
    return (
      <img src={srcValue} className={className} {...rest} onClick={onClick} />
    );
  }

  // Simply concatenate thumb_url with srcValue
  const imageUrlKey = appConfig.thumb_url || "";
  const finalSrc =
    imageUrlKey && srcValue ? `${imageUrlKey}${srcValue}` : placeholderImage;

  // Create aspect ratio class based on provided ratio or default
  const aspectRatioClass = isVideoThumbnail
    ? "aspect-[365/250]"
    : aspectRatio === "1/1"
    ? "aspect-square"
    : aspectRatio === "16/9"
    ? "aspect-video"
    : `aspect-[${aspectRatio}]`;

  const imageClasses =
    `w-full h-full object-cover ${aspectRatioClass} ${className}`.trim();

  const imageStyle = {
    ...rest.style,
  };

  return (
    <img
      src={finalSrc}
      className={imageClasses}
      onClick={onClick}
      onError={(e) => {
        if (placeholderImage) {
          (e.target as HTMLImageElement).src = placeholderImage;
        }
      }}
      {...rest}
      style={imageStyle}
    />
  );
};

export default ImageConcat;
