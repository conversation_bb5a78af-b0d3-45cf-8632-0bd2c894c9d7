import React from "react";
import { useNavigate } from "react-router";

interface MenuButtonProps {
  onClick?: () => void;
  className?: string;
}

const MenuButton: React.FC<MenuButtonProps> = ({ className = "" }) => {
  const navigate = useNavigate();

  const handleClick = () => {
    navigate("/categories");
  };

  return (
    <div
      className={`w-[20px] absolute right-3 top-1/2 -translate-y-1/2 ${className}`}
      onClick={handleClick}
    >
      <img className="w-full" src="/assets/images/hamburger.png" alt="menu" />
    </div>
  );
};

export default MenuButton;
