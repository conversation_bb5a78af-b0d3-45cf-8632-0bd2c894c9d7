import { FC, ImgHTMLAttributes, useState, useEffect } from "react";
import { useGlobalAppConfig } from "@/hooks/useGlobalAppConfig";
import { fetchData, imgDecrypt } from "@/utils/utils";

interface IImage extends Omit<ImgHTMLAttributes<HTMLImageElement>, "src"> {
  srcValue: string;
  onClick?: (value: any) => void;
  size?: boolean;
  isVideoThumbnail?: boolean;
}

const Image: FC<IImage> = ({
  srcValue = "",
  onClick = () => {},
  size = false,
  isVideoThumbnail = false,
  ...rest
}) => {
  const appConfig = useGlobalAppConfig();
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const placeholderImage = isVideoThumbnail
    ? "/assets/images/video-placeholder.png"
    : "";
  const [finalSrc, setFinalSrc] = useState(placeholderImage || srcValue);

  useEffect(() => {
    // Reset states when srcValue changes
    setIsLoading(true);
    setHasError(false);

    // Handle empty source value
    if (!srcValue) {
      setIsLoading(false);
      setHasError(true);
      return;
    }

    // Handle absolute URLs directly
    if (srcValue.includes("https://") || srcValue.includes("http://")) {
      setFinalSrc(srcValue);
      setIsLoading(false);
      return;
    }

    // Simply concatenate thumb_url with srcValue
    (async () => {
      // const imageUrlKey = appConfig.thumb_url || "";
      const imageUrlKey = "https://jpg.tlxxw.cc/";
      if (imageUrlKey && srcValue) {
        let encryptUrls = `${imageUrlKey}${srcValue}.txt`;

        if (size) {
          encryptUrls = `${encryptUrls}?size=600x337`;
        }

        try {
          const res = await fetchData(encryptUrls);
          let __decrypted = "";
          if (res) {
            __decrypted = res.indexOf("data") >= 0 ? res : imgDecrypt(res);
            setFinalSrc(__decrypted);
          } else {
            setHasError(true);
            setFinalSrc(placeholderImage);
          }
        } catch (error) {
          console.error("Failed to load image:", error);
          setHasError(true);
          setFinalSrc(placeholderImage);
        } finally {
          setIsLoading(false);
        }
      }
    })();
  }, [srcValue, appConfig.thumb_url, size, isVideoThumbnail]);

  const handleClick = (e: React.MouseEvent<HTMLImageElement>) => {
    onClick(e);
  };

  const handleError = () => {
    setHasError(true);
    setFinalSrc(placeholderImage);
    setIsLoading(false);
  };

  // Don't render anything if there's an error and no placeholder
  if (hasError && !placeholderImage) {
    return null;
  }

  const imageStyle = {
    opacity: isLoading ? 0.5 : 1,
    transition: "opacity 0.3s ease",
    ...(isVideoThumbnail && {
      aspectRatio: "365/250",
      objectFit: "cover" as const,
      width: "100%",
      height: "100%",
    }),
    ...rest.style,
  };

  return (
    <img
      src={hasError ? placeholderImage : finalSrc}
      onClick={handleClick}
      onError={handleError}
      {...rest}
      style={imageStyle}
    />
  );
};

export default Image;
