import React from "react";

interface PageLoaderProps {
  /**
   * Optional text to display under the loader
   */
  text?: string;
}

/**
 * A full-page loading component using the page-loader image
 */
const PageLoader: React.FC<PageLoaderProps> = ({ text = "正在加载中..." }) => {
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        height: "100vh",
        width: "100%",
        position: "fixed",
        top: 0,
        left: 0,
        backgroundColor: "white",
        zIndex: 1000,
      }}
    >
      <img
        src="/assets/images/page-loader.png"
        alt="Loading"
        style={{
          width: "120px",
          height: "auto",
          marginBottom: "20px",
        }}
      />
      {/* <p style={{ color: "#888", fontSize: "16px" }}>{text}</p> */}
    </div>
  );
};

export default PageLoader;
