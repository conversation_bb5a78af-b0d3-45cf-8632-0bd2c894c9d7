import { FC } from "react";
import Image from "./Image";
import { Video } from "@/types/api";
import dayjs from "dayjs";
import { fmtDateSince } from "@/utils/utils";
import ImageConcat from "./ImageConcat";

interface VideoCardProps {
  video: Video;
  onClick?: (video: Video) => void;
  className?: string;
  isVip?: boolean;
  aspectRatioClass?: string;
}

const VideoCard: FC<VideoCardProps> = ({
  video,
  onClick,
  className = "",
  isVip = video.is_paid == 1,
  aspectRatioClass = "aspect-[365/250]",
}) => {
  const handleClick = () => {
    if (onClick) {
      onClick(video);
    }
  };

  // Format video duration if needed (placeholder implementation)
  const formatDuration = (url: string) => {
    // In a real implementation, you might extract duration from metadata
    // For now, returning a placeholder
    return "00:00";
  };

  return (
    <div
      className={`video-card cursor-pointer ${className}`}
      onClick={handleClick}
    >
      <div className="relative">
        <div
          className={`relative w-full ${aspectRatioClass} rounded-lg overflow-hidden`}
        >
          <ImageConcat
            srcValue={video.thumb_url}
            className="absolute inset-0 w-full h-full object-cover"
            isVideoThumbnail={true}
          />
        </div>
        {isVip && (
          <div className="absolute top-2 right-2 bg-iconColorGold text-black px-2 py-1 rounded-md text-xs font-medium">
            VIP
          </div>
        )}
        <div className="absolute w-full bottom-0 rounded-xl">
          <div className="absolute inset-x-0 bottom-0 h-16 bg-gradient-to-t from-black/80 to-transparent rounded-b-lg"></div>
          <div className="pb-2 px-2 flex items-center justify-between relative z-10">
            {/* <p className="text-white bg-[#262630CC] px-2 rounded text-xs">
              {formatDuration(video.video_url)}
            </p> */}
            <p className="text-white px-1 py-[2px] text-[10px] rounded-[2px]">
              {fmtDateSince(video.created_at)}
            </p>
          </div>
        </div>
      </div>
      <div>
        <p className="p-1 text-sm line-clamp-2">{video.title}</p>
      </div>
    </div>
  );
};

export default VideoCard;
