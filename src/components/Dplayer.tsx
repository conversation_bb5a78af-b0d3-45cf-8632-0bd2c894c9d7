import { useEffect, useRef, FC } from "react";

import DPlayer from "dplayer";

interface DPlayerProps {
  videoUrl?: string;
  posterUrl?: string;
  onPlayerInit?: (player: DPlayer) => void;
}

export enum DPlayerEvents {
  abort = "abort",
  canplay = "canplay",
  canplaythrough = "canplaythrough",
  durationchange = "durationchange",
  emptied = "emptied",
  ended = "ended",
  error = "error",
  loadeddata = "loadeddata",
  loadedmetadata = "loadedmetadata",
  loadstart = "loadstart",
  mozaudioavailable = "mozaudioavailable",
  pause = "pause",
  play = "play",
  playing = "playing",
  progress = "progress",
  ratechange = "ratechange",
  seeked = "seeked",
  seeking = "seeking",
  stalled = "stalled",
  suspend = "suspend",
  timeupdate = "timeupdate",
  volumechange = "volumechange",
  waiting = "waiting",
  screenshot = "screenshot",
  thumbnails_show = "thumbnails_show",
  thumbnails_hide = "thumbnails_hide",
  danmaku_show = "danmaku_show",
  danmaku_hide = "danmaku_hide",
  danmaku_clear = "danmaku_clear",
  danmaku_loaded = "danmaku_loaded",
  danmaku_send = "danmaku_send",
  danmaku_opacity = "danmaku_opacity",
  contextmenu_show = "contextmenu_show",
  contextmenu_hide = "contextmenu_hide",
  notice_show = "notice_show",
  notice_hide = "notice_hide",
  quality_start = "quality_start",
  quality_end = "quality_end",
  destroy = "destroy",
  resize = "resize",
  fullscreen = "fullscreen",
  fullscreen_cancel = "fullscreen_cancel",
  subtitle_show = "subtitle_show",
  subtitle_hide = "subtitle_hide",
  subtitle_change = "subtitle_change",
}

const Dplayer: FC<DPlayerProps> = ({ videoUrl, posterUrl, onPlayerInit }) => {
  const dPlayerRef = useRef<HTMLDivElement>(null);
  const playerInstanceRef = useRef<DPlayer | null>(null);

  const controlArrowKey = (e: KeyboardEvent, dp: DPlayer) => {
    // e.preventDefault();
    // left arrow
    if (e.keyCode == 37) {
      e.preventDefault();
      dp.video.currentTime = dp.video.currentTime - 10;
    }

    // right arrow
    if (e.keyCode == 39) {
      e.preventDefault();
      dp.video.currentTime = dp.video.currentTime + 10;
    }
    // up arrow
    if (e.keyCode == 38) {
      e.preventDefault();
      if (dp.video.volume > 10) {
        dp.video.volume = 10;
      }
      dp.volume(dp.video.volume, true, false);
    }

    // down arrow
    if (e.keyCode == 40) {
      e.preventDefault();
      if (dp.video.volume < 0) {
        dp.video.volume = 0;
      }
      dp.volume(dp.video.volume, true, false);
    }
  };
  const dplayerFn = () => {
    if (playerInstanceRef.current) {
      playerInstanceRef.current.destroy();
    }

    const dp = new DPlayer({
      container: dPlayerRef.current!,
      autoplay: false,
      lang: "zh-cn",

      hotkey: true,
      preload: "auto",
      // volume: 0.7,
      mutex: true,
      video: {
        url: videoUrl || "",
        pic: posterUrl || "",
        // thumbnails: panorama,
        type: "auto",
        // defaultQuality: 0,
      },
      // subtitle: {
      //   url: "https://gist.githubusercontent.com/samdutton/ca37f3adaf4e23679957b8083e061177/raw/e19399fbccbc069a2af4266e5120ae6bad62699a/sample.vtt",
      //   type: "webvtt",
      //   fontSize: "16px",
      //   bottom: "10%",
      //   color: "#b7daff",
      // },
    });

    playerInstanceRef.current = dp;

    if (onPlayerInit) {
      onPlayerInit(dp);
    }

    if (dp) {
      // Cast the video element to HTMLVideoElement
      const videoElement = dPlayerRef.current!.children[1]
        ?.children[0] as HTMLVideoElement;

      if (videoElement) {
        videoElement.setAttribute(
          "controlsList",
          "nodownload noremote footbar"
        );
        if (videoElement.textTracks && videoElement.textTracks[0]) {
          videoElement.textTracks[0].mode = "showing";
        }
      }

      const dplayerPlay: any = document.querySelector(".dplayer-mobile-play");
      dplayerPlay.style.display = "block";

      // Disable right-click context menu
      // document.addEventListener("contextmenu", function (e) {
      //   e.preventDefault();
      // });

      dp?.on(DPlayerEvents.contextmenu_show, function () {
        return false;
      });

      dp?.on(DPlayerEvents.play, function () {
        document.addEventListener("keydown", (e) => controlArrowKey(e, dp));
        dplayerPlay.style.display = "none";
      });

      dp?.on(DPlayerEvents.pause, function () {
        document.removeEventListener("keydown", (e) => controlArrowKey(e, dp));
        dplayerPlay.style.display = "block";
      });

      dp?.on(DPlayerEvents.abort, function () {
        document.addEventListener("keydown", (e) => controlArrowKey(e, dp));
      });
    }
  };

  useEffect(() => {
    if (dPlayerRef.current) {
      dplayerFn();
    }

    return () => {
      if (playerInstanceRef.current) {
        console.log("Destroying DPlayer instance");
        playerInstanceRef.current.destroy();
        playerInstanceRef.current = null;
      }
    };
  }, [videoUrl, posterUrl, onPlayerInit]);

  return <div ref={dPlayerRef}></div>;
};

export default Dplayer;
