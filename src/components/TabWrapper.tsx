import { TabBar } from "antd-mobile";
import { useNavigate } from "react-router";
import { useEffect } from "react";

import {
  AppOutline,
  ClockCircleOutline,
  GiftOutline,
  UserOutline,
} from "antd-mobile-icons";
import VipLogo from "./VipLogo";

const TabWrapper = ({ children }: { children: React.ReactNode }) => {
  const navigate = useNavigate();
  const { pathname } = location;

  // Add or remove vip-theme class based on pathname
  useEffect(() => {
    const htmlElement = document.documentElement;
    if (pathname === "/vip") {
      htmlElement.classList.add("vip-theme");
    } else {
      htmlElement.classList.remove("vip-theme");
    }

    // Cleanup on unmount
    return () => {
      htmlElement.classList.remove("vip-theme");
    };
  }, [pathname]);

  const setRouteActive = (value: string) => {
    console.log("value", value);
    navigate(value);
    // history.push(value)
  };

  const tabs = [
    {
      key: "/",
      title: <p className="text-sm">首页</p>,
      icon: <AppOutline />,
    },
    {
      key: "/welfare",
      title: <p className="text-sm">福利</p>,
      icon: <GiftOutline />,
    },
    {
      key: "/vip",
      title: <p className="text-sm">VIP专区</p>,
      icon: (
        <div className="relative w-14 h-14">
          <div className="absolute top-2 left-0 w-full h-full -mt-8 p-2 ">
            <VipLogo
              className="w-10 h-10"
              backgroundColor={pathname === "/vip" ? "#DDBC80" : undefined}
            />
          </div>
        </div>
      ),
    },
    {
      key: "/theme",
      title: <p className="text-sm">专题</p>,
      icon: <ClockCircleOutline />,
    },
    {
      key: "/me",
      title: <p className="text-sm">我的</p>,
      icon: <UserOutline />,
    },
  ];

  return (
    <>
      {children}
      <TabBar
        activeKey={pathname}
        onChange={(value) => setRouteActive(value)}
        className="z-50 bg-white w-full fixed bottom-0 lg:max-w-[30vw] tabWrapper"
      >
        {tabs.map((item) => (
          <TabBar.Item key={item.key} icon={item.icon} title={item.title} />
        ))}
      </TabBar>
    </>
  );
};

export default TabWrapper;
