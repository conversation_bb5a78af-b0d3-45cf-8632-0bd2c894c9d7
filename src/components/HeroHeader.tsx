const HeroHeader = () => {
  return (
    <>
      {/* Hero Image */}
      <div className="relative">
        {/* Hero Image - Background Image */}
        <div className="relative">
          <img
            src="/assets/images/login-header-bg-img.png"
            alt="header-bg-img"
          />
          <div
            className="absolute w-full h-full top-0 left-0 bg-textColorPrimary/50"
            aria-hidden="true"
          />
        </div>
        {/* Hero Image - Description */}
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
          <div className="flex items-center gap-2">
            <img
              src={import.meta.env.VITE_APP_LOGO || "/assets/images/logo.png"}
              alt="maomi-logo"
              className="w-16 h-16"
            />
            <div className="text-white text-2xl font-bold">
              {import.meta.env.VITE_APP_NAME || "App Name"}
            </div>
          </div>
          {/* <p className="text-right text-[24px] text-white">
                遇见下一个X站时代
              </p> */}
        </div>
      </div>
    </>
  );
};

export default HeroHeader;
