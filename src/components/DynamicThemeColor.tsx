import { Helmet } from "react-helmet";
import { useGlobalAppConfig } from "../hooks/useGlobalAppConfig";
import { themeColors } from "./ThemeColor";

type DynamicThemeColorProps = {
  color: string | keyof typeof themeColors;
};

/**
 * Enhanced version of ThemeColor that supports dynamic theming
 * Uses the primaryTheme from useGlobalAppConfig when 'primary' is specified
 */
export const DynamicThemeColor = ({ color }: DynamicThemeColorProps) => {
  const { getPrimaryThemeHexColor } = useGlobalAppConfig();

  // If color is primary, use the dynamic primary theme color
  const themeColor =
    color === "primary"
      ? getPrimaryThemeHexColor()
      : color in themeColors
      ? themeColors[color as keyof typeof themeColors]
      : color;

  return (
    <Helmet>
      <meta
        name="theme-color"
        media="(prefers-color-scheme: light)"
        content={themeColor}
      />
      <meta
        name="theme-color"
        media="(prefers-color-scheme: dark)"
        content={themeColor}
      />
    </Helmet>
  );
};
