import { Navigate, useLocation, Outlet } from "react-router";
import { useUser } from "../contexts/UserContext";
import { PageLoader } from "./common";

const ProtectedLayout = () => {
  const location = useLocation();
  const { isAuthenticated, isLoading } = useUser();

  if (isLoading) {
    return <PageLoader />;
  }

  if (!isAuthenticated) {
    // Redirect to login page but save the attempted url
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  return <Outlet />;
};

export default ProtectedLayout;
