import { FC, useRef, useState, useEffect, useCallback } from "react";
import Dplayer, { DPlayerEvents } from "./Dplayer";
import { addVidKeyParam2 } from "@/utils/utils";
import { useGlobalAppConfig } from "@/hooks/useGlobalAppConfig";
import { Mo<PERSON>, Button } from "antd-mobile";
import type DPlayer from "dplayer"; // Import DPlayer type correctly
import { useNavigate } from "react-router"; // <-- Import useNavigate

interface VideoPlayerProps {
  videoUrl: string;
  thumbUrl?: string;
  baseUrl?: string;
  isPaid?: boolean; // Add isPaid prop
}

const VideoPlayer: FC<VideoPlayerProps> = ({
  videoUrl,
  thumbUrl,
  isPaid = false, // Default to false
}) => {
  const appConfig = useGlobalAppConfig();
  const baseUrl = appConfig.m3u8_url;
  const [alertShown, setAlertShown] = useState(false);
  // Ref to store the DPlayer instance
  const playerRef = useRef<DPlayer | null>(null);
  // Ref to store the listener functions to ensure correct removal
  const timeUpdateListenerRef = useRef<(() => void) | null>(null);
  const playListenerRef = useRef<(() => void) | null>(null); // Ref for play listener
  const navigate = useNavigate(); // <-- Initialize useNavigate
  const [isVipModalVisible, setIsVipModalVisible] = useState(false); // <-- State for modal visibility

  // Function to handle time updates
  const handleTimeUpdate = useCallback(() => {
    const player = playerRef.current;
    if (
      isPaid &&
      !alertShown &&
      player &&
      player.video &&
      player.video.currentTime >= 30
    ) {
      console.log("Paid video reached 30 seconds, pausing and showing alert.");
      player.pause(); // Pause the video
      setAlertShown(true); // Prevent showing alert multiple times
      showVipPrompt();
    }
  }, [isPaid, alertShown, navigate]);

  // Function to handle play event (reset alertShown if playing before 30s)
  const handlePlay = useCallback(() => {
    const player = playerRef.current;
    if (player && player.video && player.video.currentTime < 30) {
      console.log("Video playing before 30s, ensuring alert is reset.");
      setAlertShown(false);
    }
  }, []);

  // Callback for Dplayer component to pass the instance
  const handlePlayerInit = useCallback(
    (player: DPlayer) => {
      console.log("DPlayer instance received in VideoPlayer");
      playerRef.current = player;

      // Store listener functions in refs
      timeUpdateListenerRef.current = handleTimeUpdate;
      playListenerRef.current = handlePlay;

      if (player.video) {
        // Add listeners
        player.video.addEventListener(
          "timeupdate",
          timeUpdateListenerRef.current
        );
        player.video.addEventListener("play", playListenerRef.current);
        console.log("Attached timeupdate and play listeners.");
      } else {
        console.warn("Player video element not found immediately after init.");
      }
    },
    [handleTimeUpdate, handlePlay]
  );

  // Effect for cleanup when component unmounts or videoUrl changes
  useEffect(() => {
    const player = playerRef.current;
    const timeListener = timeUpdateListenerRef.current;
    const playListener = playListenerRef.current;
    // Reset alert state when video changes
    setAlertShown(false);

    return () => {
      // Cleanup listeners
      if (player && player.video) {
        if (timeListener) {
          console.log("Cleaning up timeupdate listener.");
          player.video.removeEventListener("timeupdate", timeListener);
        }
        if (playListener) {
          console.log("Cleaning up play listener.");
          player.video.removeEventListener("play", playListener);
        }
      }
    };
    // Depend on videoUrl to re-run setup/cleanup when the source changes
  }, [videoUrl]);

  let finalPath = null;

  // Process videoUrl to handle leading slashes
  let processedVideoUrl = videoUrl;
  //   if (processedVideoUrl && processedVideoUrl[0] === "/") {
  //     processedVideoUrl = processedVideoUrl.slice(1);
  //   }

  // Clean up any double slashes using regex
  processedVideoUrl = processedVideoUrl.replace(/\/+/g, "/");

  // Add base URL and video key parameter
  const finalVideoUrl = `${baseUrl}${processedVideoUrl}${addVidKeyParam2(
    processedVideoUrl
  )}`;
  console.log("finalVideoUrl", finalVideoUrl);
  // Process the poster URL
  const processedPosterUrl = thumbUrl
    ? `${appConfig.thumb_url || ""}${thumbUrl}`
    : "";

  // --- Updated showVipPrompt to just set state ---
  const showVipPrompt = useCallback(() => {
    setIsVipModalVisible(true);
  }, []);

  useEffect(() => {
    const player = playerRef.current;
    if (
      isPaid &&
      !alertShown &&
      player &&
      player.video &&
      player.video.currentTime >= 30
    ) {
      console.log("Paid video reached 30 seconds, pausing and showing alert.");
      player.pause(); // Pause the video
      setAlertShown(true); // Prevent showing alert multiple times
      showVipPrompt(); // Call the function to set modal state
    }
  }, [isPaid, alertShown, navigate, showVipPrompt]); // <-- Added showVipPrompt dependency

  // --- Modal Action Handlers ---
  const handleModalCancel = () => {
    setIsVipModalVisible(false);
  };

  const handleModalConfirm = () => {
    navigate("/me/member-center");
    setIsVipModalVisible(false);
  };

  return (
    <>
      <Dplayer
        videoUrl={finalVideoUrl}
        posterUrl={processedPosterUrl}
        onPlayerInit={handlePlayerInit}
      />

      <Modal
        visible={isVipModalVisible}
        title="消息提示"
        content={
          <>
            <div className="text-center text-sm text-gray-600 px-2">
              VIP视频30秒免费观看已结束, 如需继续观看此视频,
              请点击充值按钮,付费后观看后续精彩片段
            </div>
            <div className="text-center text-sm  px-2 flex justify-center gap-2 mt-4">
              <Button color="default" block onClick={handleModalCancel}>
                取消
              </Button>
              <Button color="gold" block onClick={handleModalConfirm}>
                前往充值
              </Button>
            </div>
          </>
        }
        closeOnAction={true}
        closeOnMaskClick={false}
        bodyClassName="pb-0"
        className="vip-modal"
      />
    </>
  );
};

export default VideoPlayer;
