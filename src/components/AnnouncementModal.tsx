import { Modal } from "antd-mobile";
import { useEffect, useState } from "react";
import AnnouncementTitle from "./AnnouncementTitle";
import { useNotices } from "@/hooks/useApp";
import { Notice } from "@/types/api";
import { useGlobalAppConfig } from "@/hooks/useGlobalAppConfig";

interface IAnnouncementModal {
  visible: boolean;
  onClose: () => void;
  notices: Notice[];
}

const AnnouncementModal = ({
  visible,
  onClose,
  notices,
}: IAnnouncementModal) => {
  const { primaryThemeColors } = useGlobalAppConfig();
  const [currentNoticeIndex, setCurrentNoticeIndex] = useState(0);
  const currentNotice = notices[currentNoticeIndex];

  const handleNext = () => {
    if (currentNoticeIndex < notices.length - 1) {
      setCurrentNoticeIndex(currentNoticeIndex + 1);
    }
  };

  const handlePrev = () => {
    if (currentNoticeIndex > 0) {
      setCurrentNoticeIndex(currentNoticeIndex - 1);
    }
  };

  // Derive gradient colors from theme
  const gradientStyle = {
    background: `linear-gradient(to right, rgb(${primaryThemeColors.gradientColorPrimary1}), rgb(${primaryThemeColors.gradientColorPrimary2}))`,
  };

  return (
    <Modal
      visible={visible}
      className="announcementModal"
      content={
        <div className="relative">
          <AnnouncementTitle className="absolute left-2 -top-[39px] w-[150px]" />
          {/* Header */}
          <div
            style={gradientStyle}
            className="text-white p-2 rounded-t-lg pt-4"
          >
            <h4 className="ml-2 text-xl font-bold">Announcement details</h4>
            <div
              className="absolute right-3 top-3 font-bold text-xl cursor-pointer"
              onClick={onClose}
            >
              ×
            </div>
          </div>

          {/* Body */}
          <div className="overflow-y-auto h-[calc(100dvh-400px)]">
            {/* Pagination - Top right */}
            {notices.length > 1 && (
              <div className="flex items-center justify-end gap-1 pt-2 pr-3 text-xs">
                <button
                  className={`bg-gray-100 text-gray-700 py-0.5 px-2 rounded-sm ${
                    currentNoticeIndex === 0
                      ? "opacity-50 cursor-not-allowed"
                      : ""
                  }`}
                  onClick={handlePrev}
                  disabled={currentNoticeIndex === 0}
                >
                  ←
                </button>
                <span className="text-xs">
                  {currentNoticeIndex + 1}/{notices.length}
                </span>
                <button
                  className={`bg-gray-100 text-gray-700 py-0.5 px-2 rounded-sm ${
                    currentNoticeIndex === notices.length - 1
                      ? "opacity-50 cursor-not-allowed"
                      : ""
                  }`}
                  onClick={handleNext}
                  disabled={currentNoticeIndex === notices.length - 1}
                >
                  →
                </button>
              </div>
            )}

            <div className="px-4 py-3 text-textColorPrimary flex flex-col gap-4">
              {currentNotice && (
                <div className="mb-3">
                  {currentNotice.image_url && (
                    <div className="my-2">
                      <img
                        src={currentNotice.image_url}
                        alt="Announcement"
                        className="w-full rounded"
                      />
                    </div>
                  )}
                  {currentNotice.description && (
                    <p>{currentNotice.description}</p>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Button - Moved outside scrollable area to be at very bottom */}
          <div className="flex justify-center py-4 border-t border-gray-100">
            <button
              className="bg-iconColorPrimary text-white py-2 px-10 rounded-lg"
              onClick={() => {
                window.open(currentNotice.redirect_url, "_blank");
              }}
            >
              点击查看
            </button>
          </div>
        </div>
      }
      closeOnAction
      showCloseButton={false}
      onClose={onClose}
    />
  );
};

const AnnouncementModalController = () => {
  const [visible, setVisible] = useState(false);
  const { data } = useNotices(1);
  console.log(data);
  useEffect(() => {
    // const hasSeenAnnouncement = localStorage.getItem("hasSeenAnnouncement");
    const hasSeenAnnouncement = false;
    if (data?.notices && data.notices.length > 0 && !hasSeenAnnouncement) {
      setVisible(true);
    }
  }, [data]);

  const handleClose = () => {
    localStorage.setItem("hasSeenAnnouncement", "true");
    setVisible(false);
  };

  return data?.notices && data.notices.length > 0 ? (
    <AnnouncementModal
      visible={visible}
      onClose={handleClose}
      notices={data.notices}
    />
  ) : null;
};

export default AnnouncementModalController;
