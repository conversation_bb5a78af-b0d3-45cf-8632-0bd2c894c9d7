import { ReactNode } from "react";
import { DynamicThemeColor } from "../components/DynamicThemeColor";
import MenuButton from "../components/common/MenuButton";

interface MainLayoutProps {
  header: ReactNode;
  children: ReactNode;
}

const MainLayout = ({ header, children }: MainLayoutProps) => {
  return (
    <>
      <DynamicThemeColor color="primary" />
      {/* Header Menu */}
      <div className="fixed lg:max-w-[30vw] w-full top-0 z-20 gradient-primary flex-none">
        <div className="h-12 flex items-center pr-10 relative">
          <div className="flex-1 overflow-hidden">{header}</div>
          <MenuButton />
        </div>
      </div>
      {/* Body */}
      <div className="pt-12 pb-16">{children}</div>
    </>
  );
};

export default MainLayout;
