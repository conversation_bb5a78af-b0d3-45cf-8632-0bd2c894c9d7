import { ReactNode } from "react";
import { ThemeColor } from "../components/ThemeColor";
import MenuButton from "../components/common/MenuButton";

interface MainLayoutProps {
  header: ReactNode;
  children: ReactNode;
}

const MainLayout = ({ header, children }: MainLayoutProps) => {
  return (
    <div className="h-dvh flex flex-col bg-white">
      <ThemeColor color="gold" />
      {/* Header Menu */}
      <div className="fixed lg:max-w-[30vw] w-full top-0 z-20 gradient-gold flex-none">
        <div className="h-12 flex items-center pr-10 relative">
          <div className="flex-1 overflow-hidden">{header}</div>
          <MenuButton />
        </div>
      </div>
      {/* Body */}
      <div className="flex-1 mt-12">{children}</div>
    </div>
  );
};

export default MainLayout;
