import { useState, useEffect } from "react";
import { useGlobalAppConfig } from "../hooks/useGlobalAppConfig";
import { PrimaryThemeKey, primaryThemes } from "../config/themes";
import { Button, Space, Toast } from "antd-mobile";
import { useNavigate } from "react-router";
/**
 * Example component demonstrating how to work with the dynamic theming system
 * This is just for demonstration purposes and is not meant to be used in production
 */
const ThemeExample = () => {
  const { primaryTheme, primaryThemeColors, updatePrimaryTheme } =
    useGlobalAppConfig();
  const navigate = useNavigate();
  const [selectedTheme, setSelectedTheme] = useState<PrimaryThemeKey>(
    primaryTheme as PrimaryThemeKey
  );

  // Update local state when primaryTheme changes (e.g., from localStorage)
  useEffect(() => {
    setSelectedTheme(primaryTheme as PrimaryThemeKey);
  }, [primaryTheme]);

  // This would typically come from an API call or state management system
  const handleThemeChange = (theme: PrimaryThemeKey) => {
    // Update the app config context with the new theme
    updatePrimaryTheme(theme);

    // Update local state for UI purposes
    setSelectedTheme(theme);

    // Show toast notification
    Toast.show({
      content: `Theme changed to ${theme} and saved!`,
      position: "bottom",
      duration: 1000,
    });

    // Note: The actual CSS variable updates are now handled by ThemeProvider
    // which reacts to changes in the primaryTheme value from the context
    // Theme is also automatically saved to localStorage by the provider
  };

  return (
    <div className="p-4">
      <h1 className="text-xl font-bold mb-4 text-textColorPrimary">
        Dynamic Theme Example
      </h1>

      <div className="mb-4">
        <p className="mb-2">
          Current theme: <strong>{primaryTheme}</strong>
          <span className="text-gray-500 text-xs ml-2">
            (saved to localStorage)
          </span>
        </p>
        <div className="flex items-center gap-2 mb-2">
          <span>Primary color:</span>
          <div
            className="w-6 h-6 rounded-full"
            style={{
              backgroundColor: `rgb(${primaryThemeColors.textColorPrimary})`,
            }}
          />
        </div>
        <div className="flex items-center gap-2 mb-2">
          <span>Primary dark color:</span>
          <div
            className="w-6 h-6 rounded-full"
            style={{
              backgroundColor: `rgb(${primaryThemeColors.textColorPrimaryDark})`,
            }}
          />
        </div>
        <div className="flex items-center gap-2">
          <span>Icon color:</span>
          <div
            className="w-6 h-6 rounded-full"
            style={{
              backgroundColor: `rgb(${primaryThemeColors.iconColorPrimary})`,
            }}
          />
        </div>
      </div>

      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-2">Theme preview</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 rounded-lg border">
            <h3 className="text-textColorPrimary font-medium">
              Text using primary color
            </h3>
            <button className="primayBtn px-4 py-1 mt-2">Primary Button</button>
            <div className="mt-2 gradient-primary h-20 rounded-lg flex items-center justify-center text-white">
              Gradient Background
            </div>
          </div>
        </div>
      </div>

      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-2">Gradient Colors</h2>
        <div className="flex gap-2">
          <div
            className="w-16 h-16 rounded-lg flex items-center justify-center text-xs text-white"
            style={{
              background: `rgb(${primaryThemeColors.gradientColorPrimary1})`,
            }}
          >
            Primary1
          </div>
          <div
            className="w-16 h-16 rounded-lg flex items-center justify-center text-xs text-white"
            style={{
              background: `rgb(${primaryThemeColors.gradientColorPrimary2})`,
            }}
          >
            Primary2
          </div>
          <div
            className="w-16 h-16 rounded-lg flex items-center justify-center text-xs text-white"
            style={{
              background: `rgb(${primaryThemeColors.gradientColorPrimary3})`,
            }}
          >
            Primary3
          </div>
        </div>
      </div>

      <Space wrap>
        {Object.keys(primaryThemes).map((theme) => (
          <Button
            key={theme}
            onClick={() => handleThemeChange(theme as PrimaryThemeKey)}
            color={theme === primaryTheme ? "primary" : "default"}
          >
            {theme} theme
          </Button>
        ))}
      </Space>

      {/* redirect to home */}
      <Button onClick={() => navigate("/")}>Go to Home</Button>
    </div>
  );
};

export default ThemeExample;
