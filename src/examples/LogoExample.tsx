import React from "react";
import { useGlobalAppConfig } from "../hooks/useGlobalAppConfig";
import { PrimaryThemeKey, primaryThemes } from "../config/themes";
import { Button, Space, Divider } from "antd-mobile";
import VipLogo from "../components/VipLogo";

/**
 * Example component demonstrating how VipLogo uses the dynamic theme
 */
const LogoExample = () => {
  const { primaryTheme, primaryThemeColors, updatePrimaryTheme } =
    useGlobalAppConfig();

  // Function to update theme
  const handleThemeChange = (theme: PrimaryThemeKey) => {
    updatePrimaryTheme(theme);
  };

  return (
    <div className="p-4">
      <h1 className="text-xl font-bold mb-4 text-textColorPrimary">
        Dynamic Logo Example
      </h1>

      <p className="mb-4">
        This example shows how the VipLogo component automatically updates its
        color based on the selected theme.
      </p>

      <div className="flex flex-col items-center mb-8">
        <div className="flex gap-8 mb-6">
          <div className="flex flex-col items-center">
            <p className="mb-2 text-center">Default VipLogo</p>
            <VipLogo size={100} />
            <p className="mt-2 text-xs text-gray-500">
              Uses current theme color
            </p>
          </div>

          <div className="flex flex-col items-center">
            <p className="mb-2 text-center">Custom color VipLogo</p>
            <VipLogo size={100} backgroundColor="#9D5596" />
            <p className="mt-2 text-xs text-gray-500">Uses custom color</p>
          </div>
        </div>
      </div>

      <Divider>Available Themes</Divider>

      <div className="mb-4">
        <p className="text-center mb-4">
          Current theme:{" "}
          <strong className="text-textColorPrimary">{primaryTheme}</strong>
        </p>

        <Space wrap className="flex justify-center">
          {Object.keys(primaryThemes).map((theme) => (
            <Button
              key={theme}
              onClick={() => handleThemeChange(theme as PrimaryThemeKey)}
              color={theme === primaryTheme ? "primary" : "default"}
            >
              {theme} theme
            </Button>
          ))}
        </Space>
      </div>

      <div className="mt-8 p-4 border rounded-lg bg-gray-50">
        <h3 className="text-lg font-medium mb-2 text-textColorPrimary">
          How It Works
        </h3>
        <p className="text-sm">
          The VipLogo component uses the <code>useGlobalAppConfig</code> hook to
          access the current theme color. When you change the theme, the logo
          automatically updates to match the new theme.
        </p>
        <p className="text-sm mt-2">
          You can also override the theme by passing a{" "}
          <code>backgroundColor</code> prop directly to the component, as shown
          in the second example.
        </p>
      </div>
    </div>
  );
};

export default LogoExample;
