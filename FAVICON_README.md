# Favicon Generation

This project includes automatic favicon generation from logo files.

## How it works

The favicon generation is integrated into the main prompt configuration script (`scripts/prompt-config.js`) and runs automatically when you build or run the development server.

### Generated Files

When you select a logo number (1-30), the system will generate the following favicon files in the `public/` directory:

- `favicon.ico` - Standard ICO favicon (32x32)
- `favicon-16x16.png` - 16x16 PNG favicon
- `favicon-32x32.png` - 32x32 PNG favicon  
- `favicon-48x48.png` - 48x48 PNG favicon
- `apple-touch-icon.png` - 180x180 Apple touch icon
- `android-chrome-192x192.png` - 192x192 Android icon
- `android-chrome-512x512.png` - 512x512 Android icon
- `site.webmanifest` - Web app manifest file

### HTML Integration

The `index.html` file has been updated to include all the necessary favicon links:

```html
<link rel="icon" type="image/x-icon" href="/favicon.ico" />
<link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
<link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
<link rel="manifest" href="/site.webmanifest" />
```

### Manual Generation

You can also generate favicons manually using:

```bash
npm run generate-favicon [logo_number]
```

For example:
```bash
npm run generate-favicon 5
```

This will generate favicons from `logo/5.png`.

### Requirements

- Source logo files should be in PNG format
- Logo files should be located in the `logo/` directory
- Logo files should be named `1.png`, `2.png`, ..., `30.png`
- The `sharp` package is used for image processing

### Environment Variables

You can set the logo number via environment variable:

```bash
VITE_LOGO_NUMBER=5 npm run dev
```

### Browser Support

The generated favicons support:
- Modern browsers (PNG favicons)
- Legacy browsers (ICO favicon)
- Apple devices (Apple touch icon)
- Android devices (Android chrome icons)
- Progressive Web Apps (Web manifest)
